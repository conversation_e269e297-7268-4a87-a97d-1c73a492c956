package com.homework.assistant.shared.data.model

import kotlinx.serialization.Serializable

/**
 * 学习统计数据模型
 */
@Serializable
data class LearningStatistics(
    val totalWords: Int = 0,                    // 总生字数
    val masteredWords: Int = 0,                 // 已掌握生字数
    val totalRecitations: Int = 0,              // 总背诵内容数
    val masteredRecitations: Int = 0,           // 已掌握背诵内容数
    val totalStudyTime: Long = 0,               // 总学习时间（分钟）
    val streakDays: Int = 0,                    // 连续学习天数
    val averageAccuracy: Float = 0f,            // 平均正确率
    val weeklyProgress: List<Float> = emptyList(), // 本周每日进度
    val monthlyProgress: List<Float> = emptyList(), // 本月每日进度
    val lastStudyDate: Long = 0,                // 最后学习日期
    val totalSessions: Int = 0,                 // 总学习会话数
    val averageSessionTime: Long = 0,           // 平均会话时间（分钟）
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 日常学习记录
 */
@Serializable
data class DailyRecord(
    val date: String,                           // 日期 (YYYY-MM-DD)
    val studyTime: Long = 0,                    // 学习时间（分钟）
    val wordsLearned: Int = 0,                  // 学习的生字数
    val recitationsLearned: Int = 0,            // 学习的背诵内容数
    val accuracy: Float = 0f,                   // 当日正确率
    val sessionsCount: Int = 0,                 // 会话数量
    val achievements: List<String> = emptyList(), // 当日获得的成就
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * 学习成就
 */
@Serializable
data class Achievement(
    val id: String,
    val title: String,                          // 成就标题
    val description: String,                    // 成就描述
    val icon: String,                           // 成就图标
    val type: AchievementType,                  // 成就类型
    val requirement: Int,                       // 达成要求
    val isUnlocked: Boolean = false,            // 是否已解锁
    val unlockedAt: Long? = null,               // 解锁时间
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * 成就类型
 */
@Serializable
enum class AchievementType {
    WORDS_LEARNED,      // 学习生字数量
    RECITATIONS_LEARNED, // 学习背诵内容数量
    STUDY_TIME,         // 学习时间
    STREAK_DAYS,        // 连续学习天数
    ACCURACY,           // 正确率
    SESSIONS,           // 会话数量
    SPECIAL             // 特殊成就
}

/**
 * 学习目标
 */
@Serializable
data class LearningGoal(
    val id: String,
    val title: String,                          // 目标标题
    val description: String,                    // 目标描述
    val type: GoalType,                         // 目标类型
    val targetValue: Int,                       // 目标值
    val currentValue: Int = 0,                  // 当前值
    val deadline: Long? = null,                 // 截止时间
    val isCompleted: Boolean = false,           // 是否完成
    val completedAt: Long? = null,              // 完成时间
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 目标类型
 */
@Serializable
enum class GoalType {
    DAILY_WORDS,        // 每日生字
    DAILY_RECITATIONS,  // 每日背诵
    DAILY_TIME,         // 每日学习时间
    WEEKLY_WORDS,       // 每周生字
    WEEKLY_RECITATIONS, // 每周背诵
    WEEKLY_TIME,        // 每周学习时间
    MONTHLY_WORDS,      // 每月生字
    MONTHLY_RECITATIONS, // 每月背诵
    MONTHLY_TIME,       // 每月学习时间
    CUSTOM              // 自定义目标
}
