package com.homework.assistant.shared.data.repository

import com.homework.assistant.shared.data.model.UserPreferences
import com.homework.assistant.shared.data.model.ThemeSettings
import com.homework.assistant.shared.data.model.LearningPreferences
import com.homework.assistant.shared.data.model.NotificationPreferences
import kotlinx.coroutines.flow.Flow

/**
 * 用户偏好设置仓库接口
 */
interface PreferencesRepository {
    
    /**
     * 获取用户偏好设置
     */
    fun getUserPreferences(): Flow<UserPreferences>
    
    /**
     * 更新用户偏好设置
     */
    suspend fun updateUserPreferences(preferences: UserPreferences): Result<Unit>
    
    /**
     * 获取主题设置
     */
    fun getThemeSettings(): Flow<ThemeSettings>
    
    /**
     * 更新主题设置
     */
    suspend fun updateThemeSettings(themeSettings: ThemeSettings): Result<Unit>
    
    /**
     * 获取学习偏好设置
     */
    fun getLearningPreferences(): Flow<LearningPreferences>
    
    /**
     * 更新学习偏好设置
     */
    suspend fun updateLearningPreferences(preferences: LearningPreferences): Result<Unit>
    
    /**
     * 获取通知偏好设置
     */
    fun getNotificationPreferences(): Flow<NotificationPreferences>
    
    /**
     * 更新通知偏好设置
     */
    suspend fun updateNotificationPreferences(preferences: NotificationPreferences): Result<Unit>
    
    /**
     * 获取特定偏好值
     */
    suspend fun getBooleanPreference(key: String, defaultValue: Boolean = false): Boolean
    suspend fun getIntPreference(key: String, defaultValue: Int = 0): Int
    suspend fun getFloatPreference(key: String, defaultValue: Float = 0f): Float
    suspend fun getStringPreference(key: String, defaultValue: String = ""): String
    suspend fun getLongPreference(key: String, defaultValue: Long = 0L): Long
    
    /**
     * 设置特定偏好值
     */
    suspend fun setBooleanPreference(key: String, value: Boolean): Result<Unit>
    suspend fun setIntPreference(key: String, value: Int): Result<Unit>
    suspend fun setFloatPreference(key: String, value: Float): Result<Unit>
    suspend fun setStringPreference(key: String, value: String): Result<Unit>
    suspend fun setLongPreference(key: String, value: Long): Result<Unit>
    
    /**
     * 删除特定偏好
     */
    suspend fun removePreference(key: String): Result<Unit>
    
    /**
     * 清除所有偏好设置
     */
    suspend fun clearAllPreferences(): Result<Unit>
    
    /**
     * 重置为默认设置
     */
    suspend fun resetToDefaults(): Result<Unit>
    
    /**
     * 导出偏好设置
     */
    suspend fun exportPreferences(): Result<String>
    
    /**
     * 导入偏好设置
     */
    suspend fun importPreferences(data: String): Result<Unit>
    
    /**
     * 检查是否首次启动
     */
    suspend fun isFirstLaunch(): Boolean
    
    /**
     * 设置首次启动完成
     */
    suspend fun setFirstLaunchCompleted(): Result<Unit>
    
    /**
     * 检查教程是否完成
     */
    suspend fun isTutorialCompleted(): Boolean
    
    /**
     * 设置教程完成
     */
    suspend fun setTutorialCompleted(): Result<Unit>
}
