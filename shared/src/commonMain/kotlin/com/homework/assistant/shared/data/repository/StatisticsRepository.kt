package com.homework.assistant.shared.data.repository

import com.homework.assistant.shared.data.model.LearningStatistics
import com.homework.assistant.shared.data.model.DailyRecord
import com.homework.assistant.shared.data.model.Achievement
import com.homework.assistant.shared.data.model.LearningGoal
import kotlinx.coroutines.flow.Flow

/**
 * 统计数据仓库接口
 */
interface StatisticsRepository {
    
    /**
     * 获取学习统计数据
     */
    fun getLearningStatistics(): Flow<LearningStatistics>
    
    /**
     * 更新学习统计数据
     */
    suspend fun updateLearningStatistics(statistics: LearningStatistics): Result<Unit>
    
    /**
     * 记录学习会话
     */
    suspend fun recordStudySession(
        studyTime: Long,
        wordsLearned: Int,
        recitationsLearned: Int,
        accuracy: Float
    ): Result<Unit>
    
    /**
     * 获取每日记录
     */
    fun getDailyRecords(startDate: String, endDate: String): Flow<List<DailyRecord>>
    
    /**
     * 获取指定日期的记录
     */
    suspend fun getDailyRecord(date: String): DailyRecord?
    
    /**
     * 添加每日记录
     */
    suspend fun insertDailyRecord(record: DailyRecord): Result<Unit>
    
    /**
     * 更新每日记录
     */
    suspend fun updateDailyRecord(record: DailyRecord): Result<Unit>
    
    /**
     * 获取所有成就
     */
    fun getAllAchievements(): Flow<List<Achievement>>
    
    /**
     * 获取已解锁的成就
     */
    fun getUnlockedAchievements(): Flow<List<Achievement>>
    
    /**
     * 解锁成就
     */
    suspend fun unlockAchievement(achievementId: String): Result<Unit>
    
    /**
     * 检查并解锁成就
     */
    suspend fun checkAndUnlockAchievements(): Result<List<Achievement>>
    
    /**
     * 获取学习目标
     */
    fun getLearningGoals(): Flow<List<LearningGoal>>
    
    /**
     * 添加学习目标
     */
    suspend fun insertLearningGoal(goal: LearningGoal): Result<Unit>
    
    /**
     * 更新学习目标
     */
    suspend fun updateLearningGoal(goal: LearningGoal): Result<Unit>
    
    /**
     * 删除学习目标
     */
    suspend fun deleteLearningGoal(goalId: String): Result<Unit>
    
    /**
     * 更新目标进度
     */
    suspend fun updateGoalProgress(goalId: String, progress: Int): Result<Unit>
    
    /**
     * 获取学习趋势数据
     */
    suspend fun getLearningTrends(days: Int): Result<List<DailyRecord>>
    
    /**
     * 获取学习排名（如果有多用户）
     */
    suspend fun getLearningRanking(): Result<Int>
    
    /**
     * 导出统计数据
     */
    suspend fun exportStatistics(): Result<String>
    
    /**
     * 导入统计数据
     */
    suspend fun importStatistics(data: String): Result<Unit>
    
    /**
     * 重置统计数据
     */
    suspend fun resetStatistics(): Result<Unit>
}
