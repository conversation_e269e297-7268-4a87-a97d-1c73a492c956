package com.homework.assistant.shared.data.model

import kotlinx.serialization.Serializable

/**
 * 用户偏好设置数据模型
 */
@Serializable
data class UserPreferences(
    // 外观设置
    val isDarkTheme: Boolean = false,           // 是否使用深色主题
    val language: String = "zh-CN",             // 语言设置
    val fontSize: FontSize = FontSize.MEDIUM,   // 字体大小
    
    // 语音设置
    val voiceSpeed: Float = 1.0f,               // 语音速度 (0.5-2.0)
    val voiceVolume: Float = 1.0f,              // 语音音量 (0.0-1.0)
    val voiceEnabled: Boolean = true,           // 是否启用语音
    
    // 通知设置
    val isNotificationEnabled: Boolean = true,  // 是否启用通知
    val dailyReminderTime: String = "19:00",    // 每日提醒时间
    val reminderDays: List<Int> = listOf(1,2,3,4,5,6,7), // 提醒日期 (1-7)
    val achievementNotification: Boolean = true, // 成就通知
    
    // 学习设置
    val autoNext: Boolean = true,               // 自动下一题
    val showHints: Boolean = true,              // 显示提示
    val maxHints: Int = 3,                      // 最大提示次数
    val strictMode: Boolean = false,            // 严格模式
    val similarityThreshold: Float = 0.8f,      // 相似度阈值
    
    // 数据设置
    val autoBackup: Boolean = true,             // 自动备份
    val backupFrequency: BackupFrequency = BackupFrequency.DAILY, // 备份频率
    val syncEnabled: Boolean = false,           // 同步启用
    val cacheEnabled: Boolean = true,           // 缓存启用
    val maxCacheSize: Long = 100 * 1024 * 1024, // 最大缓存大小 (100MB)
    
    // 隐私设置
    val analyticsEnabled: Boolean = true,       // 分析数据收集
    val crashReportEnabled: Boolean = true,     // 崩溃报告
    val usageDataEnabled: Boolean = true,       // 使用数据收集
    
    // 其他设置
    val firstLaunch: Boolean = true,            // 是否首次启动
    val tutorialCompleted: Boolean = false,     // 是否完成教程
    val lastBackupTime: Long = 0,               // 最后备份时间
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 字体大小枚举
 */
@Serializable
enum class FontSize {
    SMALL,      // 小
    MEDIUM,     // 中
    LARGE,      // 大
    EXTRA_LARGE // 特大
}

/**
 * 备份频率枚举
 */
@Serializable
enum class BackupFrequency {
    NEVER,      // 从不
    DAILY,      // 每日
    WEEKLY,     // 每周
    MONTHLY     // 每月
}

/**
 * 应用主题设置
 */
@Serializable
data class ThemeSettings(
    val isDarkTheme: Boolean = false,           // 是否深色主题
    val followSystem: Boolean = true,           // 跟随系统
    val primaryColor: String = "#6750A4",       // 主色调
    val accentColor: String = "#625B71",        // 强调色
    val customThemeEnabled: Boolean = false,    // 自定义主题
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 学习偏好设置
 */
@Serializable
data class LearningPreferences(
    // 听写设置
    val dictationAutoNext: Boolean = true,      // 听写自动下一个
    val dictationShowPinyin: Boolean = false,   // 显示拼音提示
    val dictationRepeatCount: Int = 2,           // 重复次数
    val dictationSpeed: Float = 1.0f,           // 听写语速
    
    // 背诵设置
    val recitationAutoNext: Boolean = true,     // 背诵自动下一句
    val recitationShowHints: Boolean = true,    // 显示提示
    val recitationMaxHints: Int = 3,            // 最大提示次数
    val recitationSimilarity: Float = 0.8f,     // 相似度要求
    
    // 练习设置
    val practiceMode: PracticeMode = PracticeMode.NORMAL, // 练习模式
    val shuffleQuestions: Boolean = false,       // 随机题目
    val showProgress: Boolean = true,           // 显示进度
    val enableTimer: Boolean = false,           // 启用计时器
    
    // 反馈设置
    val immediateCorrection: Boolean = true,    // 立即纠错
    val showExplanation: Boolean = true,        // 显示解释
    val encouragementEnabled: Boolean = true,   // 鼓励语
    val soundEffects: Boolean = true,           // 音效
    
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 练习模式枚举
 */
@Serializable
enum class PracticeMode {
    NORMAL,     // 普通模式
    CHALLENGE,  // 挑战模式
    REVIEW,     // 复习模式
    EXAM        // 考试模式
}

/**
 * 通知偏好设置
 */
@Serializable
data class NotificationPreferences(
    val enabled: Boolean = true,               // 通知总开关
    val dailyReminder: Boolean = true,         // 每日提醒
    val reminderTime: String = "19:00",        // 提醒时间
    val weekendReminder: Boolean = false,      // 周末提醒
    val achievementNotification: Boolean = true, // 成就通知
    val progressNotification: Boolean = true,  // 进度通知
    val motivationalQuotes: Boolean = true,    // 励志语句
    val sound: Boolean = true,                 // 通知声音
    val vibration: Boolean = true,             // 震动
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)
