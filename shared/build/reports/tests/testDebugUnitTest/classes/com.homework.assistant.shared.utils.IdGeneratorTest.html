<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class com.homework.assistant.shared.utils.IdGeneratorTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.homework.assistant.shared.utils.IdGeneratorTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.homework.assistant.shared.utils.html">com.homework.assistant.shared.utils</a> &gt; IdGeneratorTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">9</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.001s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">分类ID应该有正确的前缀</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">学习会话ID应该有正确的前缀</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">带前缀的ID应该包含正确的前缀</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">生字ID应该有正确的前缀</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">生成的ID应该只包含允许的字符</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">生成的ID应该有指定的长度</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">生成的ID应该有正确的长度</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">背诵ID应该有正确的前缀</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">连续生成的ID应该不相同</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.7</a> at 2025年7月3日 00:01:59</p>
</div>
</div>
</body>
</html>
