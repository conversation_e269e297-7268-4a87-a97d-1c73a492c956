<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.homework.assistant.shared.recitation.RecitationServiceTest" tests="10" skipped="0" failures="0" errors="0" timestamp="2025-07-02T16:01:59" hostname="zxnapdeMacBook-Pro.local" time="0.01">
  <properties/>
  <testcase name="提交正确背诵应该记录结果并进入下一句" classname="com.homework.assistant.shared.recitation.RecitationServiceTest" time="0.004"/>
  <testcase name="提交错误背诵应该记录错误结果" classname="com.homework.assistant.shared.recitation.RecitationServiceTest" time="0.001"/>
  <testcase name="获取进度应该返回正确的统计信息" classname="com.homework.assistant.shared.recitation.RecitationServiceTest" time="0.001"/>
  <testcase name="开始背诵应该创建会话并播报第一句" classname="com.homework.assistant.shared.recitation.RecitationServiceTest" time="0.001"/>
  <testcase name="给出提示应该返回提示信息" classname="com.homework.assistant.shared.recitation.RecitationServiceTest" time="0.0"/>
  <testcase name="处理语音指令应该执行对应操作" classname="com.homework.assistant.shared.recitation.RecitationServiceTest" time="0.001"/>
  <testcase name="暂停和恢复背诵应该正确更新状态" classname="com.homework.assistant.shared.recitation.RecitationServiceTest" time="0.0"/>
  <testcase name="下一句指令应该切换到下一句" classname="com.homework.assistant.shared.recitation.RecitationServiceTest" time="0.001"/>
  <testcase name="上一句指令应该切换到上一句" classname="com.homework.assistant.shared.recitation.RecitationServiceTest" time="0.001"/>
  <testcase name="重新开始背诵应该重置状态" classname="com.homework.assistant.shared.recitation.RecitationServiceTest" time="0.0"/>
  <system-out><![CDATA[Debug - isCorrect: false
Debug - score: 70
Debug - similarity: 0.7
Debug - feedback: 需要再练习，相似度：70%
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
