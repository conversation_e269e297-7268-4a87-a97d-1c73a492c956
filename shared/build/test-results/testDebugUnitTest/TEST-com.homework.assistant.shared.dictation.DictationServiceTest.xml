<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.homework.assistant.shared.dictation.DictationServiceTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-07-02T16:01:59" hostname="zxnapdeMacBook-Pro.local" time="0.012">
  <properties/>
  <testcase name="开始听写应该创建会话并播报第一个生字" classname="com.homework.assistant.shared.dictation.DictationServiceTest" time="0.004"/>
  <testcase name="下一个生字指令应该切换到下一个生字" classname="com.homework.assistant.shared.dictation.DictationServiceTest" time="0.001"/>
  <testcase name="获取进度应该返回正确的统计信息" classname="com.homework.assistant.shared.dictation.DictationServiceTest" time="0.003"/>
  <testcase name="提交正确答案应该记录结果并进入下一个生字" classname="com.homework.assistant.shared.dictation.DictationServiceTest" time="0.0"/>
  <testcase name="处理语音指令应该执行对应操作" classname="com.homework.assistant.shared.dictation.DictationServiceTest" time="0.001"/>
  <testcase name="暂停和恢复听写应该正确更新状态" classname="com.homework.assistant.shared.dictation.DictationServiceTest" time="0.001"/>
  <testcase name="提交错误答案应该记录错误结果" classname="com.homework.assistant.shared.dictation.DictationServiceTest" time="0.0"/>
  <testcase name="上一个生字指令应该切换到上一个生字" classname="com.homework.assistant.shared.dictation.DictationServiceTest" time="0.001"/>
  <system-out><![CDATA[Debug - currentIndex: 0
Debug - totalCount: 2
Debug - correctCount: 1
Debug - wrongCount: 0
Debug - completionRate: 0.5
Debug - accuracyRate: 1.0
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
