package com.homework.assistant.ui.screens.dictation

import androidx.lifecycle.viewModelScope
import com.homework.assistant.shared.data.model.Word
import com.homework.assistant.shared.dictation.DictationService
import com.homework.assistant.shared.dictation.DictationSession
import com.homework.assistant.shared.dictation.DictationState
import com.homework.assistant.shared.dictation.DictationSettings
import com.homework.assistant.shared.dictation.DictationResult
import com.homework.assistant.shared.dictation.DictationProgress
import com.homework.assistant.shared.dictation.DictationSessionState
import com.homework.assistant.ui.base.BaseViewModel
import com.homework.assistant.ui.base.UiState
import com.homework.assistant.ui.base.UiEvent
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 听写ViewModel
 */
class DictationViewModel(
    private val dictationService: DictationService
) : BaseViewModel<DictationUiState, DictationUiEvent>() {
    
    override val _uiState = MutableStateFlow(
        DictationUiState(
            session = null,
            currentWord = null,
            progress = DictationProgress(0, 0, 0, 0, 0, 0, 0, 0),
            isPlaying = false,
            isListening = false,
            lastResult = null,
            userInput = "",
            showMeaning = false,
            currentMeaning = "",
            availableCommands = emptyList()
        )
    )
    
    init {
        // 监听听写状态变化
        viewModelScope.launch {
            dictationService.getDictationState().collectLatest { dictationState ->
                updateState { currentState ->
                    currentState.copy(
                        session = dictationState.session,
                        currentWord = dictationState.currentWord,
                        progress = dictationState.progress,
                        isPlaying = dictationState.isPlaying,
                        isListening = dictationState.isListening,
                        lastResult = dictationState.lastResult,
                        availableCommands = dictationState.availableCommands
                    )
                }
            }
        }
    }
    
    override fun handleEvent(event: DictationUiEvent) {
        when (event) {
            is DictationUiEvent.StartDictation -> startDictation(event.words, event.settings)
            is DictationUiEvent.SpeakCurrentWord -> speakCurrentWord()
            is DictationUiEvent.RepeatWord -> repeatWord()
            is DictationUiEvent.SpeakPinyin -> speakPinyin()
            is DictationUiEvent.ShowMeaning -> showMeaning()
            is DictationUiEvent.HideMeaning -> hideMeaning()
            is DictationUiEvent.NextWord -> nextWord()
            is DictationUiEvent.PreviousWord -> previousWord()
            is DictationUiEvent.UpdateUserInput -> updateUserInput(event.input)
            is DictationUiEvent.SubmitAnswer -> submitAnswer()
            is DictationUiEvent.PauseDictation -> pauseDictation()
            is DictationUiEvent.ResumeDictation -> resumeDictation()
            is DictationUiEvent.FinishDictation -> finishDictation()
            is DictationUiEvent.HandleVoiceCommand -> handleVoiceCommand(event.command)
        }
    }
    
    private fun startDictation(words: List<Word>, settings: DictationSettings) {
        launchSafely(showLoading = true) {
            val session = dictationService.startDictation(words, settings)
            // 状态会通过Flow自动更新
        }
    }
    
    private fun speakCurrentWord() {
        launchSafely {
            dictationService.speakCurrentWord()
        }
    }
    
    private fun repeatWord() {
        launchSafely {
            dictationService.repeatCurrentWord()
        }
    }
    
    private fun speakPinyin() {
        launchSafely {
            dictationService.speakWordPinyin()
        }
    }
    
    private fun showMeaning() {
        launchSafely {
            val result = dictationService.showWordMeaning()
            if (result.isSuccess) {
                updateState { it.copy(showMeaning = true, currentMeaning = result.getOrNull() ?: "") }
            }
        }
    }
    
    private fun hideMeaning() {
        updateState { it.copy(showMeaning = false, currentMeaning = "") }
    }
    
    private fun nextWord() {
        launchSafely {
            dictationService.nextWord()
            // 清空用户输入
            updateState { it.copy(userInput = "") }
        }
    }
    
    private fun previousWord() {
        launchSafely {
            dictationService.previousWord()
            // 清空用户输入
            updateState { it.copy(userInput = "") }
        }
    }
    
    private fun updateUserInput(input: String) {
        updateState { it.copy(userInput = input) }
    }
    
    private fun submitAnswer() {
        val currentInput = _uiState.value.userInput
        if (currentInput.isBlank()) return
        
        launchSafely {
            val result = dictationService.submitAnswer(currentInput)
            if (result.isSuccess) {
                // 清空用户输入
                updateState { it.copy(userInput = "") }
            }
        }
    }
    
    private fun pauseDictation() {
        launchSafely {
            dictationService.pauseDictation()
        }
    }
    
    private fun resumeDictation() {
        launchSafely {
            dictationService.resumeDictation()
        }
    }
    
    private fun finishDictation() {
        launchSafely {
            dictationService.finishDictation()
        }
    }
    
    private fun handleVoiceCommand(command: String) {
        launchSafely {
            dictationService.handleVoiceCommand(command)
        }
    }
}

/**
 * 听写UI状态
 */
data class DictationUiState(
    val session: DictationSession?,
    val currentWord: Word?,
    val progress: DictationProgress,
    val isPlaying: Boolean,
    val isListening: Boolean,
    val lastResult: DictationResult?,
    val userInput: String,
    val showMeaning: Boolean,
    val currentMeaning: String,
    val availableCommands: List<String>
) : UiState {
    
    val canSubmit: Boolean
        get() = userInput.isNotBlank() && isListening
    
    val isSessionActive: Boolean
        get() = session != null && session.state != DictationSessionState.COMPLETED && session.state != DictationSessionState.CANCELLED
    
    val canNavigate: Boolean
        get() = session != null && !isPlaying
}

/**
 * 听写UI事件
 */
sealed class DictationUiEvent : UiEvent {
    data class StartDictation(val words: List<Word>, val settings: DictationSettings = DictationSettings()) : DictationUiEvent()
    data object SpeakCurrentWord : DictationUiEvent()
    data object RepeatWord : DictationUiEvent()
    data object SpeakPinyin : DictationUiEvent()
    data object ShowMeaning : DictationUiEvent()
    data object HideMeaning : DictationUiEvent()
    data object NextWord : DictationUiEvent()
    data object PreviousWord : DictationUiEvent()
    data class UpdateUserInput(val input: String) : DictationUiEvent()
    data object SubmitAnswer : DictationUiEvent()
    data object PauseDictation : DictationUiEvent()
    data object ResumeDictation : DictationUiEvent()
    data object FinishDictation : DictationUiEvent()
    data class HandleVoiceCommand(val command: String) : DictationUiEvent()
}
