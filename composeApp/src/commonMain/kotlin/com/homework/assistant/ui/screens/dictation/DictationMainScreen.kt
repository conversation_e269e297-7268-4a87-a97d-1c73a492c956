package com.homework.assistant.ui.screens.dictation

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.homework.assistant.shared.data.model.Word
import com.homework.assistant.shared.dictation.DictationSettings
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.components.EmptyState
import com.homework.assistant.ui.components.FeatureCard
import com.homework.assistant.ui.navigation.NavigationManager
import com.homework.assistant.ui.navigation.Screen
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 听写主页面
 */
@Composable
fun DictationMainScreen(
    navigationManager: NavigationManager
) {
    // 模拟数据 - 实际应该从Repository获取
    val sampleWords = remember {
        listOf(
            Word(
                id = "1",
                character = "学",
                pinyin = "xué",
                meaning = "学习、求知",
                category = "一年级上册"
            ),
            Word(
                id = "2",
                character = "习",
                pinyin = "xí",
                meaning = "练习、温习",
                category = "一年级上册"
            ),
            Word(
                id = "3",
                character = "生",
                pinyin = "shēng",
                meaning = "生命、出生",
                category = "一年级上册"
            ),
            Word(
                id = "4",
                character = "字",
                pinyin = "zì",
                meaning = "文字、汉字",
                category = "一年级上册"
            )
        )
    }

    var selectedWords by remember { mutableStateOf(setOf<String>()) }
    var showSettings by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "听写生字",
            onNavigationClick = {
                navigationManager.navigateBack()
            },
            actions = {
                IconButton(
                    onClick = { showSettings = true }
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "设置"
                    )
                }

                IconButton(
                    onClick = {
                        // 导航到生字管理
                        navigationManager.navigateTo(Screen.Management.Words.LIST)
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "管理生字"
                    )
                }
            }
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 选择提示
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "选择要听写的生字",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "已选择 ${selectedWords.size} 个生字",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 生字列表
            if (sampleWords.isEmpty()) {
                EmptyState(
                    message = "还没有生字，请先添加一些生字",
                    icon = "📝",
                    actionText = "添加生字",
                    onAction = {
                        navigationManager.navigateTo(Screen.Management.Words.ADD)
                    }
                )
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.weight(1f)
                ) {
                    items(sampleWords) { word ->
                        WordSelectionCard(
                            word = word,
                            isSelected = selectedWords.contains(word.id),
                            onSelectionChange = { isSelected ->
                                selectedWords = if (isSelected) {
                                    selectedWords + word.id
                                } else {
                                    selectedWords - word.id
                                }
                            }
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 开始听写按钮
            Button(
                onClick = {
                    if (selectedWords.isNotEmpty()) {
                        val wordsToDict = sampleWords.filter { selectedWords.contains(it.id) }
                        val route = Screen.Dictation.sessionRoute(wordsToDict.map { it.id })
                        navigationManager.navigateTo(route)
                    }
                },
                enabled = selectedWords.isNotEmpty(),
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = null
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("开始听写 (${selectedWords.size}个生字)")
            }
        }
    }

    // 设置对话框
    if (showSettings) {
        DictationSettingsDialog(
            onDismiss = { showSettings = false },
            onConfirm = { settings ->
                showSettings = false
                // TODO: 保存设置
            }
        )
    }
}

/**
 * 生字选择卡片
 */
@Composable
private fun WordSelectionCard(
    word: Word,
    isSelected: Boolean,
    onSelectionChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            androidx.compose.foundation.BorderStroke(
                2.dp,
                MaterialTheme.colorScheme.primary
            )
        } else null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 选择框
            Checkbox(
                checked = isSelected,
                onCheckedChange = onSelectionChange
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 生字信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = word.character,
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        }
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = word.pinyin,
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = word.meaning,
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )

                if (word.category.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = word.category,
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.outline
                        }
                    )
                }
            }
        }
    }
}

/**
 * 听写设置对话框
 */
@Composable
private fun DictationSettingsDialog(
    onDismiss: () -> Unit,
    onConfirm: (DictationSettings) -> Unit
) {
    var speakingSpeed by remember { mutableFloatStateOf(1.0f) }
    var repeatCount by remember { mutableIntStateOf(2) }
    var autoNext by remember { mutableStateOf(true) }
    var showPinyin by remember { mutableStateOf(false) }
    var enableHints by remember { mutableStateOf(true) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("听写设置")
        },
        text = {
            Column {
                // 语音速度
                Text(
                    text = "语音速度: ${String.format("%.1f", speakingSpeed)}x",
                    style = MaterialTheme.typography.bodyMedium
                )
                Slider(
                    value = speakingSpeed,
                    onValueChange = { speakingSpeed = it },
                    valueRange = 0.5f..2.0f,
                    steps = 14
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 重复次数
                Text(
                    text = "重复次数: $repeatCount",
                    style = MaterialTheme.typography.bodyMedium
                )
                Slider(
                    value = repeatCount.toFloat(),
                    onValueChange = { repeatCount = it.toInt() },
                    valueRange = 1f..5f,
                    steps = 3
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 开关选项
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("自动下一个")
                    Switch(
                        checked = autoNext,
                        onCheckedChange = { autoNext = it }
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("显示拼音提示")
                    Switch(
                        checked = showPinyin,
                        onCheckedChange = { showPinyin = it }
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("启用提示功能")
                    Switch(
                        checked = enableHints,
                        onCheckedChange = { enableHints = it }
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val settings = DictationSettings(
                        speakingSpeed = speakingSpeed,
                        repeatCount = repeatCount,
                        autoNext = autoNext,
                        showPinyin = showPinyin,
                        enableHints = enableHints
                    )
                    onConfirm(settings)
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 听写会话页面
 */
@Composable
fun DictationSessionScreen(
    wordIds: List<String>,
    sessionId: String?,
    navigationManager: NavigationManager
) {
    // TODO: 实际应该注入ViewModel
    // val viewModel: DictationViewModel = koinViewModel()

    // 模拟状态 - 实际应该从ViewModel获取
    var currentWordIndex by remember { mutableIntStateOf(0) }
    var userInput by remember { mutableStateOf("") }
    var showMeaning by remember { mutableStateOf(false) }
    var isPlaying by remember { mutableStateOf(false) }

    // 模拟生字数据
    val sampleWords = remember {
        listOf(
            Word(
                id = "1",
                character = "学",
                pinyin = "xué",
                meaning = "学习、求知",
                category = "一年级上册"
            ),
            Word(
                id = "2",
                character = "习",
                pinyin = "xí",
                meaning = "练习、温习",
                category = "一年级上册"
            )
        )
    }

    val currentWord = if (currentWordIndex < sampleWords.size) {
        sampleWords[currentWordIndex]
    } else null

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "听写进行中",
            onNavigationClick = {
                // 显示确认对话框
                navigationManager.navigateBack()
            }
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 进度指示器
            DictationProgressCard(
                currentIndex = currentWordIndex,
                totalCount = sampleWords.size,
                correctCount = 0,
                wrongCount = 0
            )

            Spacer(modifier = Modifier.height(24.dp))

            // 当前生字显示区域
            if (currentWord != null) {
                CurrentWordDisplay(
                    word = currentWord,
                    showMeaning = showMeaning,
                    isPlaying = isPlaying,
                    onToggleMeaning = { showMeaning = !showMeaning }
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 用户输入区域
            UserInputSection(
                userInput = userInput,
                onInputChange = { userInput = it },
                onSubmit = {
                    // TODO: 提交答案
                    userInput = ""
                    if (currentWordIndex < sampleWords.size - 1) {
                        currentWordIndex++
                    }
                },
                enabled = !isPlaying
            )

            Spacer(modifier = Modifier.height(24.dp))

            // 控制按钮区域
            DictationControlButtons(
                onSpeak = {
                    isPlaying = true
                    // 模拟播放完成
                    kotlinx.coroutines.GlobalScope.launch {
                        kotlinx.coroutines.delay(2000)
                        isPlaying = false
                    }
                },
                onRepeat = {
                    isPlaying = true
                    kotlinx.coroutines.GlobalScope.launch {
                        kotlinx.coroutines.delay(2000)
                        isPlaying = false
                    }
                },
                onPinyin = {
                    // TODO: 播放拼音
                },
                onPrevious = {
                    if (currentWordIndex > 0) {
                        currentWordIndex--
                        userInput = ""
                    }
                },
                onNext = {
                    if (currentWordIndex < sampleWords.size - 1) {
                        currentWordIndex++
                        userInput = ""
                    }
                },
                onFinish = {
                    // TODO: 完成听写
                    navigationManager.navigateBack()
                },
                canPrevious = currentWordIndex > 0,
                canNext = currentWordIndex < sampleWords.size - 1,
                isPlaying = isPlaying
            )
        }
    }
}

/**
 * 听写进度卡片
 */
@Composable
private fun DictationProgressCard(
    currentIndex: Int,
    totalCount: Int,
    correctCount: Int,
    wrongCount: Int
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "进度",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )

                Text(
                    text = "${currentIndex + 1} / $totalCount",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            LinearProgressIndicator(
                progress = { if (totalCount > 0) (currentIndex + 1).toFloat() / totalCount else 0f },
                modifier = Modifier.fillMaxWidth(),
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "✅",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = "$correctCount",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "❌",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = "$wrongCount",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "📊",
                        style = MaterialTheme.typography.titleMedium
                    )
                    val accuracy = if (correctCount + wrongCount > 0) {
                        (correctCount * 100) / (correctCount + wrongCount)
                    } else 0
                    Text(
                        text = "$accuracy%",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 听写结果页面
 */
@Composable
fun DictationResultScreen(
    sessionId: String,
    navigationManager: NavigationManager
) {
    // 模拟结果数据
    val mockResults = remember {
        listOf(
            DictationResultItem(
                word = Word(
                    id = "1",
                    character = "学",
                    pinyin = "xué",
                    meaning = "学习、求知"
                ),
                userAnswer = "学",
                isCorrect = true,
                score = 100,
                timeSpent = 3000
            ),
            DictationResultItem(
                word = Word(
                    id = "2",
                    character = "习",
                    pinyin = "xí",
                    meaning = "练习、温习"
                ),
                userAnswer = "xi",
                isCorrect = false,
                score = 0,
                timeSpent = 5000
            )
        )
    }

    val totalScore = mockResults.map { it.score }.average().toInt()
    val correctCount = mockResults.count { it.isCorrect }
    val totalCount = mockResults.size

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "听写结果",
            onNavigationClick = {
                navigationManager.navigateBack()
            }
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 总体结果卡片
            item {
                DictationSummaryCard(
                    totalScore = totalScore,
                    correctCount = correctCount,
                    totalCount = totalCount,
                    totalTime = mockResults.sumOf { it.timeSpent }
                )
            }

            // 详细结果列表
            items(mockResults) { result ->
                DictationResultCard(result = result)
            }

            // 操作按钮
            item {
                Column {
                    Button(
                        onClick = {
                            // 重新听写
                            navigationManager.navigateBack()
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = null
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("重新听写")
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    OutlinedButton(
                        onClick = {
                            // 返回主页
                            navigationManager.navigateBackTo(Screen.HOME)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Home,
                            contentDescription = null
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("返回主页")
                    }
                }
            }
        }
    }
}

/**
 * 听写结果项数据类
 */
private data class DictationResultItem(
    val word: Word,
    val userAnswer: String,
    val isCorrect: Boolean,
    val score: Int,
    val timeSpent: Long
)
