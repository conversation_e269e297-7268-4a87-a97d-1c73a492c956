package com.homework.assistant.ui.screens.management

import androidx.lifecycle.viewModelScope
import com.homework.assistant.shared.data.model.Word
import com.homework.assistant.shared.data.model.WordCategory
import com.homework.assistant.shared.data.model.Recitation
import com.homework.assistant.shared.data.model.RecitationCategory
import com.homework.assistant.shared.data.model.RecitationType
import com.homework.assistant.shared.data.repository.WordRepository
import com.homework.assistant.shared.data.repository.RecitationRepository
import com.homework.assistant.ui.base.BaseViewModel
import com.homework.assistant.ui.base.UiState
import com.homework.assistant.ui.base.UiEvent
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 内容管理ViewModel
 */
class ManagementViewModel(
    private val wordRepository: WordRepository,
    private val recitationRepository: RecitationRepository
) : BaseViewModel<ManagementUiState, ManagementUiEvent>() {
    
    override val _uiState = MutableStateFlow(
        ManagementUiState(
            words = emptyList(),
            wordCategories = emptyList(),
            recitations = emptyList(),
            recitationCategories = emptyList(),
            selectedCategory = null,
            searchQuery = "",
            isSearching = false
        )
    )
    
    init {
        loadData()
    }
    
    override fun handleEvent(event: ManagementUiEvent) {
        when (event) {
            is ManagementUiEvent.LoadWords -> loadWords()
            is ManagementUiEvent.LoadRecitations -> loadRecitations()
            is ManagementUiEvent.LoadWordCategories -> loadWordCategories()
            is ManagementUiEvent.LoadRecitationCategories -> loadRecitationCategories()
            is ManagementUiEvent.SearchWords -> searchWords(event.query)
            is ManagementUiEvent.SearchRecitations -> searchRecitations(event.query)
            is ManagementUiEvent.SelectCategory -> selectCategory(event.categoryId)
            is ManagementUiEvent.AddWord -> addWord(event.word)
            is ManagementUiEvent.UpdateWord -> updateWord(event.word)
            is ManagementUiEvent.DeleteWord -> deleteWord(event.wordId)
            is ManagementUiEvent.AddRecitation -> addRecitation(event.recitation)
            is ManagementUiEvent.UpdateRecitation -> updateRecitation(event.recitation)
            is ManagementUiEvent.DeleteRecitation -> deleteRecitation(event.recitationId)
            is ManagementUiEvent.AddWordCategory -> addWordCategory(event.category)
            is ManagementUiEvent.UpdateWordCategory -> updateWordCategory(event.category)
            is ManagementUiEvent.DeleteWordCategory -> deleteWordCategory(event.categoryId)
            is ManagementUiEvent.AddRecitationCategory -> addRecitationCategory(event.category)
            is ManagementUiEvent.UpdateRecitationCategory -> updateRecitationCategory(event.category)
            is ManagementUiEvent.DeleteRecitationCategory -> deleteRecitationCategory(event.categoryId)
        }
    }
    
    private fun loadData() {
        loadWords()
        loadRecitations()
        loadWordCategories()
        loadRecitationCategories()
    }
    
    private fun loadWords() {
        viewModelScope.launch {
            wordRepository.getAllWords().collectLatest { words ->
                updateState { it.copy(words = words) }
            }
        }
    }
    
    private fun loadRecitations() {
        viewModelScope.launch {
            recitationRepository.getAllRecitations().collectLatest { recitations ->
                updateState { it.copy(recitations = recitations) }
            }
        }
    }
    
    private fun loadWordCategories() {
        viewModelScope.launch {
            wordRepository.getAllCategories().collectLatest { categories ->
                updateState { it.copy(wordCategories = categories) }
            }
        }
    }
    
    private fun loadRecitationCategories() {
        viewModelScope.launch {
            recitationRepository.getAllCategories().collectLatest { categories ->
                updateState { it.copy(recitationCategories = categories) }
            }
        }
    }
    
    private fun searchWords(query: String) {
        updateState { it.copy(searchQuery = query, isSearching = true) }
        
        if (query.isBlank()) {
            loadWords()
            updateState { it.copy(isSearching = false) }
            return
        }
        
        viewModelScope.launch {
            wordRepository.searchWords(query).collectLatest { words ->
                updateState { it.copy(words = words, isSearching = false) }
            }
        }
    }
    
    private fun searchRecitations(query: String) {
        updateState { it.copy(searchQuery = query, isSearching = true) }
        
        if (query.isBlank()) {
            loadRecitations()
            updateState { it.copy(isSearching = false) }
            return
        }
        
        viewModelScope.launch {
            recitationRepository.searchRecitations(query).collectLatest { recitations ->
                updateState { it.copy(recitations = recitations, isSearching = false) }
            }
        }
    }
    
    private fun selectCategory(categoryId: String?) {
        updateState { it.copy(selectedCategory = categoryId) }
        
        if (categoryId == null) {
            loadWords()
            loadRecitations()
        } else {
            viewModelScope.launch {
                wordRepository.getWordsByCategory(categoryId).collectLatest { words ->
                    updateState { it.copy(words = words) }
                }
            }
            viewModelScope.launch {
                recitationRepository.getRecitationsByCategory(categoryId).collectLatest { recitations ->
                    updateState { it.copy(recitations = recitations) }
                }
            }
        }
    }
    
    private fun addWord(word: Word) {
        launchSafely(showLoading = true) {
            wordRepository.insertWord(word)
        }
    }
    
    private fun updateWord(word: Word) {
        launchSafely(showLoading = true) {
            wordRepository.updateWord(word)
        }
    }
    
    private fun deleteWord(wordId: String) {
        launchSafely(showLoading = true) {
            wordRepository.deleteWord(wordId)
        }
    }
    
    private fun addRecitation(recitation: Recitation) {
        launchSafely(showLoading = true) {
            recitationRepository.insertRecitation(recitation)
        }
    }
    
    private fun updateRecitation(recitation: Recitation) {
        launchSafely(showLoading = true) {
            recitationRepository.updateRecitation(recitation)
        }
    }
    
    private fun deleteRecitation(recitationId: String) {
        launchSafely(showLoading = true) {
            recitationRepository.deleteRecitation(recitationId)
        }
    }
    
    private fun addWordCategory(category: WordCategory) {
        launchSafely(showLoading = true) {
            wordRepository.insertCategory(category)
        }
    }
    
    private fun updateWordCategory(category: WordCategory) {
        launchSafely(showLoading = true) {
            wordRepository.updateCategory(category)
        }
    }
    
    private fun deleteWordCategory(categoryId: String) {
        launchSafely(showLoading = true) {
            wordRepository.deleteCategory(categoryId)
        }
    }
    
    private fun addRecitationCategory(category: RecitationCategory) {
        launchSafely(showLoading = true) {
            recitationRepository.insertCategory(category)
        }
    }
    
    private fun updateRecitationCategory(category: RecitationCategory) {
        launchSafely(showLoading = true) {
            recitationRepository.updateCategory(category)
        }
    }
    
    private fun deleteRecitationCategory(categoryId: String) {
        launchSafely(showLoading = true) {
            recitationRepository.deleteCategory(categoryId)
        }
    }
}

/**
 * 内容管理UI状态
 */
data class ManagementUiState(
    val words: List<Word>,
    val wordCategories: List<WordCategory>,
    val recitations: List<Recitation>,
    val recitationCategories: List<RecitationCategory>,
    val selectedCategory: String?,
    val searchQuery: String,
    val isSearching: Boolean
) : UiState

/**
 * 内容管理UI事件
 */
sealed class ManagementUiEvent : UiEvent {
    data object LoadWords : ManagementUiEvent()
    data object LoadRecitations : ManagementUiEvent()
    data object LoadWordCategories : ManagementUiEvent()
    data object LoadRecitationCategories : ManagementUiEvent()
    data class SearchWords(val query: String) : ManagementUiEvent()
    data class SearchRecitations(val query: String) : ManagementUiEvent()
    data class SelectCategory(val categoryId: String?) : ManagementUiEvent()
    data class AddWord(val word: Word) : ManagementUiEvent()
    data class UpdateWord(val word: Word) : ManagementUiEvent()
    data class DeleteWord(val wordId: String) : ManagementUiEvent()
    data class AddRecitation(val recitation: Recitation) : ManagementUiEvent()
    data class UpdateRecitation(val recitation: Recitation) : ManagementUiEvent()
    data class DeleteRecitation(val recitationId: String) : ManagementUiEvent()
    data class AddWordCategory(val category: WordCategory) : ManagementUiEvent()
    data class UpdateWordCategory(val category: WordCategory) : ManagementUiEvent()
    data class DeleteWordCategory(val categoryId: String) : ManagementUiEvent()
    data class AddRecitationCategory(val category: RecitationCategory) : ManagementUiEvent()
    data class UpdateRecitationCategory(val category: RecitationCategory) : ManagementUiEvent()
    data class DeleteRecitationCategory(val categoryId: String) : ManagementUiEvent()
}
