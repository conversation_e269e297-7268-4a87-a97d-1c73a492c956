package com.homework.assistant.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

/**
 * 应用主题颜色定义
 */
object AppColors {
    // 主色调 - 温暖的蓝色，适合儿童应用
    val Primary = Color(0xFF4A90E2)
    val PrimaryVariant = Color(0xFF357ABD)
    val OnPrimary = Color.White
    
    // 次要色调 - 温暖的橙色
    val Secondary = Color(0xFFFF9500)
    val SecondaryVariant = Color(0xFFE6850E)
    val OnSecondary = Color.White
    
    // 背景色
    val Background = Color(0xFFFAFAFA)
    val Surface = Color.White
    val OnBackground = Color(0xFF1C1C1E)
    val OnSurface = Color(0xFF1C1C1E)
    
    // 功能色
    val Success = Color(0xFF34C759)
    val Warning = Color(0xFFFF9500)
    val Error = Color(0xFFFF3B30)
    val Info = Color(0xFF007AFF)
    
    // 中性色
    val Gray50 = Color(0xFFFAFAFA)
    val Gray100 = Color(0xFFF5F5F5)
    val Gray200 = Color(0xFFEEEEEE)
    val Gray300 = Color(0xFFE0E0E0)
    val Gray400 = Color(0xFFBDBDBD)
    val Gray500 = Color(0xFF9E9E9E)
    val Gray600 = Color(0xFF757575)
    val Gray700 = Color(0xFF616161)
    val Gray800 = Color(0xFF424242)
    val Gray900 = Color(0xFF212121)
    
    // 暗色主题
    val DarkBackground = Color(0xFF121212)
    val DarkSurface = Color(0xFF1E1E1E)
    val DarkOnBackground = Color(0xFFE1E1E1)
    val DarkOnSurface = Color(0xFFE1E1E1)
}

/**
 * 亮色主题配色方案
 */
private val LightColorScheme = lightColorScheme(
    primary = AppColors.Primary,
    onPrimary = AppColors.OnPrimary,
    primaryContainer = AppColors.PrimaryVariant,
    onPrimaryContainer = AppColors.OnPrimary,
    
    secondary = AppColors.Secondary,
    onSecondary = AppColors.OnSecondary,
    secondaryContainer = AppColors.SecondaryVariant,
    onSecondaryContainer = AppColors.OnSecondary,
    
    background = AppColors.Background,
    onBackground = AppColors.OnBackground,
    surface = AppColors.Surface,
    onSurface = AppColors.OnSurface,
    
    error = AppColors.Error,
    onError = Color.White,
    
    outline = AppColors.Gray300,
    outlineVariant = AppColors.Gray200,
    
    surfaceVariant = AppColors.Gray100,
    onSurfaceVariant = AppColors.Gray600,
    
    inverseSurface = AppColors.Gray800,
    inverseOnSurface = AppColors.Gray100,
    inversePrimary = AppColors.Primary
)

/**
 * 暗色主题配色方案
 */
private val DarkColorScheme = darkColorScheme(
    primary = AppColors.Primary,
    onPrimary = AppColors.OnPrimary,
    primaryContainer = AppColors.PrimaryVariant,
    onPrimaryContainer = AppColors.OnPrimary,
    
    secondary = AppColors.Secondary,
    onSecondary = AppColors.OnSecondary,
    secondaryContainer = AppColors.SecondaryVariant,
    onSecondaryContainer = AppColors.OnSecondary,
    
    background = AppColors.DarkBackground,
    onBackground = AppColors.DarkOnBackground,
    surface = AppColors.DarkSurface,
    onSurface = AppColors.DarkOnSurface,
    
    error = AppColors.Error,
    onError = Color.White,
    
    outline = AppColors.Gray600,
    outlineVariant = AppColors.Gray700,
    
    surfaceVariant = AppColors.Gray800,
    onSurfaceVariant = AppColors.Gray400,
    
    inverseSurface = AppColors.Gray200,
    inverseOnSurface = AppColors.Gray800,
    inversePrimary = AppColors.Primary
)

/**
 * 应用主题
 */
@Composable
fun HomeworkAssistantTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val colorScheme = if (darkTheme) {
        DarkColorScheme
    } else {
        LightColorScheme
    }
    
    MaterialTheme(
        colorScheme = colorScheme,
        typography = AppTypography,
        shapes = AppShapes,
        content = content
    )
}

/**
 * 扩展属性，方便访问自定义颜色
 */
val MaterialTheme.appColors: AppColors
    @Composable
    get() = AppColors
