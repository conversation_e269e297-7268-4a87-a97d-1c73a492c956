package com.homework.assistant.ui.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Shapes
import androidx.compose.ui.unit.dp

/**
 * 应用形状定义
 * 使用圆角设计，营造友好的视觉效果
 */
val AppShapes = Shapes(
    // 小圆角 - 用于按钮、输入框等
    small = RoundedCornerShape(8.dp),
    
    // 中等圆角 - 用于卡片、对话框等
    medium = RoundedCornerShape(12.dp),
    
    // 大圆角 - 用于大型容器、底部表单等
    large = RoundedCornerShape(16.dp)
)

/**
 * 自定义形状
 */
object CustomShapes {
    
    /**
     * 超小圆角
     */
    val ExtraSmall = RoundedCornerShape(4.dp)
    
    /**
     * 超大圆角
     */
    val ExtraLarge = RoundedCornerShape(24.dp)
    
    /**
     * 圆形
     */
    val Circle = RoundedCornerShape(50)
    
    /**
     * 顶部圆角 - 用于底部弹出框
     */
    val TopRounded = RoundedCornerShape(
        topStart = 16.dp,
        topEnd = 16.dp,
        bottomStart = 0.dp,
        bottomEnd = 0.dp
    )
    
    /**
     * 底部圆角 - 用于顶部固定区域
     */
    val BottomRounded = RoundedCornerShape(
        topStart = 0.dp,
        topEnd = 0.dp,
        bottomStart = 16.dp,
        bottomEnd = 16.dp
    )
    
    /**
     * 左侧圆角
     */
    val LeftRounded = RoundedCornerShape(
        topStart = 16.dp,
        topEnd = 0.dp,
        bottomStart = 16.dp,
        bottomEnd = 0.dp
    )
    
    /**
     * 右侧圆角
     */
    val RightRounded = RoundedCornerShape(
        topStart = 0.dp,
        topEnd = 16.dp,
        bottomStart = 0.dp,
        bottomEnd = 16.dp
    )
}
