package com.homework.assistant.ui.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel基类
 * 提供通用的状态管理和错误处理功能
 */
abstract class BaseViewModel<UiState : Any, UiEvent : Any> : ViewModel() {
    
    /**
     * UI状态
     */
    protected abstract val _uiState: MutableStateFlow<UiState>
    val uiState: StateFlow<UiState> get() = _uiState.asStateFlow()
    
    /**
     * 加载状态
     */
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    /**
     * 错误状态
     */
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    /**
     * 异常处理器
     */
    private val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        handleError(exception)
    }
    
    /**
     * 处理UI事件
     */
    abstract fun handleEvent(event: UiEvent)
    
    /**
     * 安全执行协程操作
     */
    protected fun launchSafely(
        showLoading: Boolean = false,
        block: suspend () -> Unit
    ) {
        viewModelScope.launch(exceptionHandler) {
            try {
                if (showLoading) {
                    _isLoading.value = true
                }
                clearError()
                block()
            } finally {
                if (showLoading) {
                    _isLoading.value = false
                }
            }
        }
    }
    
    /**
     * 处理错误
     */
    protected open fun handleError(throwable: Throwable) {
        _error.value = throwable.message ?: "未知错误"
        _isLoading.value = false
    }
    
    /**
     * 清除错误状态
     */
    protected fun clearError() {
        _error.value = null
    }
    
    /**
     * 更新UI状态
     */
    protected fun updateState(update: (UiState) -> UiState) {
        _uiState.value = update(_uiState.value)
    }
}

/**
 * UI状态基类
 */
interface UiState

/**
 * UI事件基类
 */
interface UiEvent

/**
 * 通用加载状态
 */
sealed class LoadingState {
    data object Idle : LoadingState()
    data object Loading : LoadingState()
    data class Success<T>(val data: T) : LoadingState()
    data class Error(val message: String) : LoadingState()
}

/**
 * 通用UI效果
 */
sealed class UiEffect {
    data class ShowMessage(val message: String) : UiEffect()
    data class ShowError(val error: String) : UiEffect()
    data object NavigateBack : UiEffect()
    data class Navigate(val route: String) : UiEffect()
}

/**
 * 带有副作用的ViewModel基类
 */
abstract class BaseViewModelWithEffects<UiState : Any, UiEvent : Any, Effect : UiEffect> : 
    BaseViewModel<UiState, UiEvent>() {
    
    /**
     * UI副作用
     */
    private val _effect = MutableStateFlow<Effect?>(null)
    val effect: StateFlow<Effect?> = _effect.asStateFlow()
    
    /**
     * 发送副作用
     */
    protected fun sendEffect(effect: Effect) {
        _effect.value = effect
    }
    
    /**
     * 清除副作用
     */
    fun clearEffect() {
        _effect.value = null
    }
}

/**
 * 分页数据状态
 */
data class PagingState<T>(
    val items: List<T> = emptyList(),
    val isLoading: Boolean = false,
    val isLoadingMore: Boolean = false,
    val hasMore: Boolean = true,
    val error: String? = null,
    val currentPage: Int = 0
) : UiState

/**
 * 表单状态基类
 */
abstract class FormState : UiState {
    abstract val isValid: Boolean
    abstract val errors: Map<String, String>
}

/**
 * 搜索状态
 */
data class SearchState<T>(
    val query: String = "",
    val results: List<T> = emptyList(),
    val isSearching: Boolean = false,
    val hasSearched: Boolean = false,
    val error: String? = null
) : UiState
