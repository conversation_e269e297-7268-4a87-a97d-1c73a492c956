package com.homework.assistant.ui.screens.recitation

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.navigation.NavigationManager

/**
 * 背诵主页面 - 占位符实现
 */
@Composable
fun RecitationMainScreen(
    navigationManager: NavigationManager
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "背诵练习",
            onNavigationClick = {
                navigationManager.navigateBack()
            }
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "📖",
                style = MaterialTheme.typography.displayLarge
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "背诵练习功能",
                style = MaterialTheme.typography.headlineMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "即将推出...",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 背诵会话页面 - 占位符实现
 */
@Composable
fun RecitationSessionScreen(
    recitationId: String,
    sessionId: String?,
    navigationManager: NavigationManager
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "背诵进行中",
            onNavigationClick = {
                navigationManager.navigateBack()
            }
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "🎤",
                style = MaterialTheme.typography.displayLarge
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "背诵会话",
                style = MaterialTheme.typography.headlineMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "背诵ID: $recitationId",
                style = MaterialTheme.typography.bodyLarge
            )
            
            if (sessionId != null) {
                Text(
                    text = "会话ID: $sessionId",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 背诵结果页面 - 占位符实现
 */
@Composable
fun RecitationResultScreen(
    sessionId: String,
    navigationManager: NavigationManager
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "背诵结果",
            onNavigationClick = {
                navigationManager.navigateBack()
            }
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "🏆",
                style = MaterialTheme.typography.displayLarge
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "背诵结果",
                style = MaterialTheme.typography.headlineMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "会话ID: $sessionId",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}
