package com.homework.assistant.ui.screens.recitation

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.homework.assistant.shared.data.model.Recitation
import com.homework.assistant.shared.recitation.RecitationSettings
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.components.EmptyState
import com.homework.assistant.ui.navigation.NavigationManager
import com.homework.assistant.ui.navigation.Screen
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 背诵主页面
 */
@Composable
fun RecitationMainScreen(
    navigationManager: NavigationManager
) {
    // 模拟数据 - 实际应该从Repository获取
    val sampleRecitations = remember {
        listOf(
            Recitation(
                id = "1",
                title = "静夜思",
                author = "李白",
                content = "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
                difficulty = 1,
                category = "唐诗"
            ),
            Recitation(
                id = "2",
                title = "春晓",
                author = "孟浩然",
                content = "春眠不觉晓，处处闻啼鸟。夜来风雨声，花落知多少。",
                difficulty = 1,
                category = "唐诗"
            ),
            Recitation(
                id = "3",
                title = "登鹳雀楼",
                author = "王之涣",
                content = "白日依山尽，黄河入海流。欲穷千里目，更上一层楼。",
                difficulty = 2,
                category = "唐诗"
            )
        )
    }

    var selectedRecitation by remember { mutableStateOf<Recitation?>(null) }
    var showSettings by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "背诵练习",
            onNavigationClick = {
                navigationManager.navigateBack()
            },
            actions = {
                IconButton(
                    onClick = { showSettings = true }
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "设置"
                    )
                }

                IconButton(
                    onClick = {
                        // 导航到背诵内容管理
                        navigationManager.navigateTo(Screen.Management.Recitations.LIST)
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "管理背诵内容"
                    )
                }
            }
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 选择提示
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "选择要背诵的内容",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = if (selectedRecitation != null) {
                            "已选择：${selectedRecitation!!.title}"
                        } else {
                            "请选择一首诗词开始背诵"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 背诵内容列表
            if (sampleRecitations.isEmpty()) {
                EmptyState(
                    message = "还没有背诵内容，请先添加一些诗词",
                    icon = "📖",
                    actionText = "添加内容",
                    onAction = {
                        navigationManager.navigateTo(Screen.Management.Recitations.ADD)
                    }
                )
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.weight(1f)
                ) {
                    items(sampleRecitations) { recitation ->
                        RecitationSelectionCard(
                            recitation = recitation,
                            isSelected = selectedRecitation?.id == recitation.id,
                            onSelectionChange = { isSelected ->
                                selectedRecitation = if (isSelected) recitation else null
                            }
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 开始背诵按钮
            Button(
                onClick = {
                    selectedRecitation?.let { recitation ->
                        val route = Screen.Recitation.sessionRoute(recitation.id)
                        navigationManager.navigateTo(route)
                    }
                },
                enabled = selectedRecitation != null,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = null
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("开始背诵")
            }
        }
    }

    // 设置对话框
    if (showSettings) {
        RecitationSettingsDialog(
            onDismiss = { showSettings = false },
            onConfirm = { settings ->
                showSettings = false
                // TODO: 保存设置
            }
        )
    }
}

/**
 * 背诵内容选择卡片
 */
@Composable
private fun RecitationSelectionCard(
    recitation: Recitation,
    isSelected: Boolean,
    onSelectionChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            androidx.compose.foundation.BorderStroke(
                2.dp,
                MaterialTheme.colorScheme.primary
            )
        } else null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 选择框
            RadioButton(
                selected = isSelected,
                onClick = { onSelectionChange(!isSelected) }
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 背诵内容信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = recitation.title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        }
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    // 难度标识
                    Surface(
                        color = when (recitation.difficulty) {
                            1 -> MaterialTheme.colorScheme.secondary
                            2 -> MaterialTheme.colorScheme.tertiary
                            else -> MaterialTheme.colorScheme.error
                        },
                        shape = MaterialTheme.shapes.small
                    ) {
                        Text(
                            text = when (recitation.difficulty) {
                                1 -> "简单"
                                2 -> "中等"
                                else -> "困难"
                            },
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onSecondary,
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = "作者：${recitation.author}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = recitation.content.take(20) + if (recitation.content.length > 20) "..." else "",
                    style = MaterialTheme.typography.bodySmall,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )

                if (recitation.category.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = recitation.category,
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.outline
                        }
                    )
                }
            }
        }
    }
}

/**
 * 背诵设置对话框
 */
@Composable
private fun RecitationSettingsDialog(
    onDismiss: () -> Unit,
    onConfirm: (RecitationSettings) -> Unit
) {
    var promptSpeed by remember { mutableFloatStateOf(1.0f) }
    var similarityThreshold by remember { mutableFloatStateOf(0.8f) }
    var enableHints by remember { mutableStateOf(true) }
    var maxHints by remember { mutableIntStateOf(3) }
    var autoNext by remember { mutableStateOf(true) }
    var strictMode by remember { mutableStateOf(false) }
    var allowSkip by remember { mutableStateOf(true) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("背诵设置")
        },
        text = {
            LazyColumn {
                item {
                    // 提示语音速度
                    Text(
                        text = "提示语音速度: ${String.format("%.1f", promptSpeed)}x",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Slider(
                        value = promptSpeed,
                        onValueChange = { promptSpeed = it },
                        valueRange = 0.5f..2.0f,
                        steps = 14
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // 相似度阈值
                    Text(
                        text = "相似度要求: ${(similarityThreshold * 100).toInt()}%",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Slider(
                        value = similarityThreshold,
                        onValueChange = { similarityThreshold = it },
                        valueRange = 0.6f..1.0f,
                        steps = 7
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // 最大提示次数
                    if (enableHints) {
                        Text(
                            text = "最大提示次数: $maxHints",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Slider(
                            value = maxHints.toFloat(),
                            onValueChange = { maxHints = it.toInt() },
                            valueRange = 1f..5f,
                            steps = 3
                        )

                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    // 开关选项
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text("启用提示功能")
                        Switch(
                            checked = enableHints,
                            onCheckedChange = { enableHints = it }
                        )
                    }

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text("自动下一句")
                        Switch(
                            checked = autoNext,
                            onCheckedChange = { autoNext = it }
                        )
                    }

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text("严格模式")
                        Switch(
                            checked = strictMode,
                            onCheckedChange = { strictMode = it }
                        )
                    }

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text("允许跳过")
                        Switch(
                            checked = allowSkip,
                            onCheckedChange = { allowSkip = it }
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val settings = RecitationSettings(
                        promptSpeed = promptSpeed,
                        similarityThreshold = similarityThreshold,
                        enableHints = enableHints,
                        maxHints = maxHints,
                        autoNext = autoNext,
                        strictMode = strictMode,
                        allowSkip = allowSkip
                    )
                    onConfirm(settings)
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 背诵会话页面
 */
@Composable
fun RecitationSessionScreen(
    recitationId: String,
    sessionId: String?,
    navigationManager: NavigationManager
) {
    // TODO: 实际应该注入ViewModel
    // val viewModel: RecitationViewModel = koinViewModel()

    // 模拟状态 - 实际应该从ViewModel获取
    var currentSentenceIndex by remember { mutableIntStateOf(0) }
    var userInput by remember { mutableStateOf("") }
    var showHint by remember { mutableStateOf(false) }
    var currentHint by remember { mutableStateOf("") }
    var isPrompting by remember { mutableStateOf(false) }

    // 模拟背诵内容数据
    val sampleRecitation = remember {
        Recitation(
            id = recitationId,
            title = "静夜思",
            author = "李白",
            content = "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
            difficulty = 1,
            category = "唐诗"
        )
    }

    val sentences = remember {
        sampleRecitation.content.split("[，。！？；：]".toRegex())
            .map { it.trim() }
            .filter { it.isNotEmpty() }
    }

    val currentSentence = if (currentSentenceIndex < sentences.size) {
        sentences[currentSentenceIndex]
    } else null

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "背诵进行中",
            onNavigationClick = {
                // 显示确认对话框
                navigationManager.navigateBack()
            }
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 进度指示器
            RecitationProgressCard(
                currentIndex = currentSentenceIndex,
                totalCount = sentences.size,
                correctCount = 0,
                wrongCount = 0,
                title = sampleRecitation.title,
                author = sampleRecitation.author
            )

            Spacer(modifier = Modifier.height(24.dp))

            // 当前句子显示区域
            if (currentSentence != null) {
                CurrentSentenceDisplay(
                    sentence = currentSentence,
                    showHint = showHint,
                    currentHint = currentHint,
                    isPrompting = isPrompting,
                    onToggleHint = {
                        if (showHint) {
                            showHint = false
                            currentHint = ""
                        } else {
                            showHint = true
                            currentHint = "提示：${currentSentence.take(2)}..."
                        }
                    }
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 用户输入区域
            RecitationInputSection(
                userInput = userInput,
                onInputChange = { userInput = it },
                onSubmit = {
                    // TODO: 提交背诵
                    userInput = ""
                    if (currentSentenceIndex < sentences.size - 1) {
                        currentSentenceIndex++
                    }
                },
                enabled = !isPrompting
            )

            Spacer(modifier = Modifier.height(24.dp))

            // 控制按钮区域
            RecitationControlButtons(
                onSpeak = {
                    isPrompting = true
                    // 模拟播放完成
                    kotlinx.coroutines.GlobalScope.launch {
                        kotlinx.coroutines.delay(2000)
                        isPrompting = false
                    }
                },
                onRepeat = {
                    isPrompting = true
                    kotlinx.coroutines.GlobalScope.launch {
                        kotlinx.coroutines.delay(2000)
                        isPrompting = false
                    }
                },
                onFullPoem = {
                    // TODO: 播放整首诗
                },
                onPrevious = {
                    if (currentSentenceIndex > 0) {
                        currentSentenceIndex--
                        userInput = ""
                    }
                },
                onNext = {
                    if (currentSentenceIndex < sentences.size - 1) {
                        currentSentenceIndex++
                        userInput = ""
                    }
                },
                onSkip = {
                    if (currentSentenceIndex < sentences.size - 1) {
                        currentSentenceIndex++
                        userInput = ""
                    }
                },
                onFinish = {
                    // TODO: 完成背诵
                    navigationManager.navigateBack()
                },
                canPrevious = currentSentenceIndex > 0,
                canNext = currentSentenceIndex < sentences.size - 1,
                canSkip = true,
                isPrompting = isPrompting
            )
        }
    }
}

/**
 * 背诵结果页面
 */
@Composable
fun RecitationResultScreen(
    sessionId: String,
    navigationManager: NavigationManager
) {
    // 模拟结果数据
    val mockResults = remember {
        listOf(
            RecitationResultItem(
                sentence = "床前明月光",
                userInput = "床前明月光",
                similarity = 1.0f,
                isCorrect = true,
                score = 100,
                timeSpent = 3000,
                hintsUsed = 0
            ),
            RecitationResultItem(
                sentence = "疑是地上霜",
                userInput = "疑是地上双",
                similarity = 0.75f,
                isCorrect = false,
                score = 75,
                timeSpent = 5000,
                hintsUsed = 1
            ),
            RecitationResultItem(
                sentence = "举头望明月",
                userInput = "举头望明月",
                similarity = 1.0f,
                isCorrect = true,
                score = 100,
                timeSpent = 2500,
                hintsUsed = 0
            ),
            RecitationResultItem(
                sentence = "低头思故乡",
                userInput = "低头思故乡",
                similarity = 1.0f,
                isCorrect = true,
                score = 100,
                timeSpent = 2800,
                hintsUsed = 0
            )
        )
    }

    val totalScore = mockResults.map { it.score }.average().toInt()
    val correctCount = mockResults.count { it.isCorrect }
    val totalCount = mockResults.size
    val totalHints = mockResults.sumOf { it.hintsUsed }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "背诵结果",
            onNavigationClick = {
                navigationManager.navigateBack()
            }
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 总体结果卡片
            item {
                RecitationSummaryCard(
                    totalScore = totalScore,
                    correctCount = correctCount,
                    totalCount = totalCount,
                    totalTime = mockResults.sumOf { it.timeSpent },
                    hintsUsed = totalHints,
                    title = "静夜思",
                    author = "李白"
                )
            }

            // 详细结果列表
            items(mockResults) { result ->
                com.homework.assistant.ui.screens.recitation.RecitationResultCard(result = result)
            }

            // 操作按钮
            item {
                Column {
                    Button(
                        onClick = {
                            // 重新背诵
                            navigationManager.navigateBack()
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = null
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("重新背诵")
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    OutlinedButton(
                        onClick = {
                            // 返回主页
                            navigationManager.navigateBackTo(Screen.HOME)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Home,
                            contentDescription = null
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("返回主页")
                    }
                }
            }
        }
    }
}

/**
 * 背诵结果项数据类
 */
data class RecitationResultItem(
    val sentence: String,
    val userInput: String,
    val similarity: Float,
    val isCorrect: Boolean,
    val score: Int,
    val timeSpent: Long,
    val hintsUsed: Int
)


