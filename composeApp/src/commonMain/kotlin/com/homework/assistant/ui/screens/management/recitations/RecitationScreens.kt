package com.homework.assistant.ui.screens.management.recitations

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.homework.assistant.shared.data.model.Recitation
import com.homework.assistant.shared.data.model.RecitationType
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.components.EmptyState
import com.homework.assistant.ui.navigation.NavigationManager
import com.homework.assistant.ui.navigation.Screen

/**
 * 背诵内容列表页面
 */
@Composable
fun RecitationListScreen(navigationManager: NavigationManager) {
    // 模拟数据 - 实际应该从ViewModel获取
    val sampleRecitations = remember {
        listOf(
            Recitation(
                id = "1",
                title = "静夜思",
                content = "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
                author = "李白",
                source = "《全唐诗》",
                category = "唐诗",
                difficulty = 1,
                estimatedTime = 5,
                tags = listOf("思乡", "月亮", "经典")
            ),
            Recitation(
                id = "2",
                title = "春晓",
                content = "春眠不觉晓，处处闻啼鸟。夜来风雨声，花落知多少。",
                author = "孟浩然",
                source = "《全唐诗》",
                category = "唐诗",
                difficulty = 1,
                estimatedTime = 5,
                tags = listOf("春天", "自然", "经典")
            ),
            Recitation(
                id = "3",
                title = "登鹳雀楼",
                content = "白日依山尽，黄河入海流。欲穷千里目，更上一层楼。",
                author = "王之涣",
                source = "《全唐诗》",
                category = "唐诗",
                difficulty = 2,
                estimatedTime = 8,
                tags = listOf("励志", "哲理", "经典")
            )
        )
    }

    var searchQuery by remember { mutableStateOf("") }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "背诵内容",
            onNavigationClick = { navigationManager.navigateBack() },
            actions = {
                IconButton(
                    onClick = {
                        navigationManager.navigateTo(Screen.Management.Recitations.ADD)
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加背诵内容"
                    )
                }
            }
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 搜索框
            OutlinedTextField(
                value = searchQuery,
                onValueChange = { searchQuery = it },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("搜索背诵内容...") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "搜索"
                    )
                },
                trailingIcon = {
                    if (searchQuery.isNotEmpty()) {
                        IconButton(
                            onClick = { searchQuery = "" }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "清除"
                            )
                        }
                    }
                }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 背诵内容列表
            if (sampleRecitations.isEmpty()) {
                EmptyState(
                    message = "还没有背诵内容，点击右上角添加",
                    icon = "📖",
                    actionText = "添加内容",
                    onAction = {
                        navigationManager.navigateTo(Screen.Management.Recitations.ADD)
                    }
                )
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        sampleRecitations.filter {
                            searchQuery.isEmpty() ||
                            it.title.contains(searchQuery, ignoreCase = true) ||
                            it.author.contains(searchQuery, ignoreCase = true) ||
                            it.content.contains(searchQuery, ignoreCase = true) ||
                            it.category.contains(searchQuery, ignoreCase = true)
                        }
                    ) { recitation ->
                        RecitationListItem(
                            recitation = recitation,
                            onEdit = {
                                navigationManager.navigateTo(Screen.Management.Recitations.editRoute(recitation.id))
                            },
                            onDelete = {
                                // TODO: 删除背诵内容
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 添加背诵内容页面
 */
@Composable
fun RecitationAddScreen(navigationManager: NavigationManager) {
    var title by remember { mutableStateOf("") }
    var content by remember { mutableStateOf("") }
    var author by remember { mutableStateOf("") }
    var source by remember { mutableStateOf("") }
    var category by remember { mutableStateOf("") }
    var difficulty by remember { mutableIntStateOf(1) }
    var estimatedTime by remember { mutableIntStateOf(5) }
    var tags by remember { mutableStateOf("") }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "添加背诵内容",
            onNavigationClick = { navigationManager.navigateBack() }
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                RecitationForm(
                    title = title,
                    onTitleChange = { title = it },
                    content = content,
                    onContentChange = { content = it },
                    author = author,
                    onAuthorChange = { author = it },
                    source = source,
                    onSourceChange = { source = it },
                    category = category,
                    onCategoryChange = { category = it },
                    difficulty = difficulty,
                    onDifficultyChange = { difficulty = it },
                    estimatedTime = estimatedTime,
                    onEstimatedTimeChange = { estimatedTime = it },
                    tags = tags,
                    onTagsChange = { tags = it }
                )
            }

            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = { navigationManager.navigateBack() },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("取消")
                    }

                    Button(
                        onClick = {
                            // TODO: 保存背诵内容
                            navigationManager.navigateBack()
                        },
                        enabled = title.isNotBlank() && content.isNotBlank(),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("保存")
                    }
                }
            }
        }
    }
}

/**
 * 编辑背诵内容页面
 */
@Composable
fun RecitationEditScreen(recitationId: String, navigationManager: NavigationManager) {
    // 模拟数据 - 实际应该从ViewModel获取
    val recitation = remember {
        Recitation(
            id = recitationId,
            title = "静夜思",
            content = "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
            author = "李白",
            source = "《全唐诗》",
            category = "唐诗",
            difficulty = 1,
            estimatedTime = 5,
            tags = listOf("思乡", "月亮", "经典")
        )
    }

    var title by remember { mutableStateOf(recitation.title) }
    var content by remember { mutableStateOf(recitation.content) }
    var author by remember { mutableStateOf(recitation.author) }
    var source by remember { mutableStateOf(recitation.source) }
    var category by remember { mutableStateOf(recitation.category) }
    var difficulty by remember { mutableIntStateOf(recitation.difficulty) }
    var estimatedTime by remember { mutableIntStateOf(recitation.estimatedTime) }
    var tags by remember { mutableStateOf(recitation.tags.joinToString(", ")) }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "编辑背诵内容",
            onNavigationClick = { navigationManager.navigateBack() }
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                RecitationForm(
                    title = title,
                    onTitleChange = { title = it },
                    content = content,
                    onContentChange = { content = it },
                    author = author,
                    onAuthorChange = { author = it },
                    source = source,
                    onSourceChange = { source = it },
                    category = category,
                    onCategoryChange = { category = it },
                    difficulty = difficulty,
                    onDifficultyChange = { difficulty = it },
                    estimatedTime = estimatedTime,
                    onEstimatedTimeChange = { estimatedTime = it },
                    tags = tags,
                    onTagsChange = { tags = it }
                )
            }

            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = { navigationManager.navigateBack() },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("取消")
                    }

                    Button(
                        onClick = {
                            // TODO: 更新背诵内容
                            navigationManager.navigateBack()
                        },
                        enabled = title.isNotBlank() && content.isNotBlank(),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("保存")
                    }
                }
            }
        }
    }
}
