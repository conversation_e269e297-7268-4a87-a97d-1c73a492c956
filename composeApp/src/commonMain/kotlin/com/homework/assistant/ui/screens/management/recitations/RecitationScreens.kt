package com.homework.assistant.ui.screens.management.recitations

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.navigation.NavigationManager

@Composable
fun RecitationListScreen(navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "背诵内容", onNavigationClick = { navigationManager.navigateBack() })
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("背诵内容列表 - 即将推出")
        }
    }
}

@Composable
fun RecitationAddScreen(navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "添加背诵内容", onNavigationClick = { navigationManager.navigateBack() })
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("添加背诵内容 - 即将推出")
        }
    }
}

@Composable
fun RecitationEditScreen(recitationId: String, navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "编辑背诵内容", onNavigationClick = { navigationManager.navigateBack() })
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("编辑背诵内容: $recitationId - 即将推出")
        }
    }
}
