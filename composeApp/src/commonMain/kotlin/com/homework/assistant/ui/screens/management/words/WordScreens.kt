package com.homework.assistant.ui.screens.management.words

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.homework.assistant.shared.data.model.Word
import com.homework.assistant.shared.data.model.WordCategory
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.components.EmptyState
import com.homework.assistant.ui.navigation.NavigationManager
import com.homework.assistant.ui.navigation.Screen

/**
 * 生字列表页面
 */
@Composable
fun WordListScreen(navigationManager: NavigationManager) {
    // 模拟数据 - 实际应该从ViewModel获取
    val sampleWords = remember {
        listOf(
            Word(
                id = "1",
                character = "学",
                pinyin = "xué",
                meaning = "学习、求知",
                category = "一年级上册",
                difficulty = 1,
                strokeCount = 8,
                radicals = "子",
                examples = listOf("学习", "学校", "学生")
            ),
            Word(
                id = "2",
                character = "习",
                pinyin = "xí",
                meaning = "练习、温习",
                category = "一年级上册",
                difficulty = 1,
                strokeCount = 3,
                radicals = "羽",
                examples = listOf("学习", "练习", "习惯")
            ),
            Word(
                id = "3",
                character = "生",
                pinyin = "shēng",
                meaning = "生命、出生",
                category = "一年级上册",
                difficulty = 1,
                strokeCount = 5,
                radicals = "生",
                examples = listOf("生活", "学生", "生日")
            )
        )
    }

    var searchQuery by remember { mutableStateOf("") }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "生字列表",
            onNavigationClick = { navigationManager.navigateBack() },
            actions = {
                IconButton(
                    onClick = {
                        navigationManager.navigateTo(Screen.Management.Words.ADD)
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加生字"
                    )
                }
            }
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 搜索框
            OutlinedTextField(
                value = searchQuery,
                onValueChange = { searchQuery = it },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("搜索生字...") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "搜索"
                    )
                },
                trailingIcon = {
                    if (searchQuery.isNotEmpty()) {
                        IconButton(
                            onClick = { searchQuery = "" }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "清除"
                            )
                        }
                    }
                }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 生字列表
            if (sampleWords.isEmpty()) {
                EmptyState(
                    message = "还没有生字，点击右上角添加",
                    icon = "📝",
                    actionText = "添加生字",
                    onAction = {
                        navigationManager.navigateTo(Screen.Management.Words.ADD)
                    }
                )
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        sampleWords.filter {
                            searchQuery.isEmpty() ||
                            it.character.contains(searchQuery) ||
                            it.pinyin.contains(searchQuery, ignoreCase = true) ||
                            it.meaning.contains(searchQuery, ignoreCase = true)
                        }
                    ) { word ->
                        WordListItem(
                            word = word,
                            onEdit = {
                                navigationManager.navigateTo(Screen.Management.Words.editRoute(word.id))
                            },
                            onDelete = {
                                // TODO: 删除生字
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 添加生字页面
 */
@Composable
fun WordAddScreen(navigationManager: NavigationManager) {
    var character by remember { mutableStateOf("") }
    var pinyin by remember { mutableStateOf("") }
    var meaning by remember { mutableStateOf("") }
    var category by remember { mutableStateOf("") }
    var difficulty by remember { mutableIntStateOf(1) }
    var strokeCount by remember { mutableIntStateOf(0) }
    var radicals by remember { mutableStateOf("") }
    var examples by remember { mutableStateOf("") }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "添加生字",
            onNavigationClick = { navigationManager.navigateBack() }
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                WordForm(
                    character = character,
                    onCharacterChange = { character = it },
                    pinyin = pinyin,
                    onPinyinChange = { pinyin = it },
                    meaning = meaning,
                    onMeaningChange = { meaning = it },
                    category = category,
                    onCategoryChange = { category = it },
                    difficulty = difficulty,
                    onDifficultyChange = { difficulty = it },
                    strokeCount = strokeCount,
                    onStrokeCountChange = { strokeCount = it },
                    radicals = radicals,
                    onRadicalsChange = { radicals = it },
                    examples = examples,
                    onExamplesChange = { examples = it }
                )
            }

            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = { navigationManager.navigateBack() },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("取消")
                    }

                    Button(
                        onClick = {
                            // TODO: 保存生字
                            navigationManager.navigateBack()
                        },
                        enabled = character.isNotBlank() && pinyin.isNotBlank() && meaning.isNotBlank(),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("保存")
                    }
                }
            }
        }
    }
}

/**
 * 编辑生字页面
 */
@Composable
fun WordEditScreen(wordId: String, navigationManager: NavigationManager) {
    // 模拟数据 - 实际应该从ViewModel获取
    val word = remember {
        Word(
            id = wordId,
            character = "学",
            pinyin = "xué",
            meaning = "学习、求知",
            category = "一年级上册",
            difficulty = 1,
            strokeCount = 8,
            radicals = "子",
            examples = listOf("学习", "学校", "学生")
        )
    }

    var character by remember { mutableStateOf(word.character) }
    var pinyin by remember { mutableStateOf(word.pinyin) }
    var meaning by remember { mutableStateOf(word.meaning) }
    var category by remember { mutableStateOf(word.category) }
    var difficulty by remember { mutableIntStateOf(word.difficulty) }
    var strokeCount by remember { mutableIntStateOf(word.strokeCount) }
    var radicals by remember { mutableStateOf(word.radicals) }
    var examples by remember { mutableStateOf(word.examples.joinToString("\n")) }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "编辑生字",
            onNavigationClick = { navigationManager.navigateBack() }
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                WordForm(
                    character = character,
                    onCharacterChange = { character = it },
                    pinyin = pinyin,
                    onPinyinChange = { pinyin = it },
                    meaning = meaning,
                    onMeaningChange = { meaning = it },
                    category = category,
                    onCategoryChange = { category = it },
                    difficulty = difficulty,
                    onDifficultyChange = { difficulty = it },
                    strokeCount = strokeCount,
                    onStrokeCountChange = { strokeCount = it },
                    radicals = radicals,
                    onRadicalsChange = { radicals = it },
                    examples = examples,
                    onExamplesChange = { examples = it }
                )
            }

            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = { navigationManager.navigateBack() },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("取消")
                    }

                    Button(
                        onClick = {
                            // TODO: 更新生字
                            navigationManager.navigateBack()
                        },
                        enabled = character.isNotBlank() && pinyin.isNotBlank() && meaning.isNotBlank(),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("保存")
                    }
                }
            }
        }
    }
}

/**
 * 生字分类页面
 */
@Composable
fun WordCategoriesScreen(navigationManager: NavigationManager) {
    // 模拟数据
    val sampleCategories = remember {
        listOf(
            WordCategory(
                id = "1",
                name = "一年级上册",
                description = "小学一年级上学期生字",
                grade = "一年级",
                semester = "上学期",
                order = 1
            ),
            WordCategory(
                id = "2",
                name = "一年级下册",
                description = "小学一年级下学期生字",
                grade = "一年级",
                semester = "下学期",
                order = 2
            ),
            WordCategory(
                id = "3",
                name = "二年级上册",
                description = "小学二年级上学期生字",
                grade = "二年级",
                semester = "上学期",
                order = 3
            )
        )
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "生字分类",
            onNavigationClick = { navigationManager.navigateBack() },
            actions = {
                IconButton(
                    onClick = {
                        // TODO: 添加分类
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加分类"
                    )
                }
            }
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(sampleCategories) { category ->
                CategoryListItem(
                    category = category,
                    onEdit = {
                        // TODO: 编辑分类
                    },
                    onDelete = {
                        // TODO: 删除分类
                    }
                )
            }
        }
    }
}
