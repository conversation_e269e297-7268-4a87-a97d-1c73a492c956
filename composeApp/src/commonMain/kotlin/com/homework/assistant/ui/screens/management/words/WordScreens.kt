package com.homework.assistant.ui.screens.management.words

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.navigation.NavigationManager

@Composable
fun WordListScreen(navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "生字列表", onNavigationClick = { navigationManager.navigateBack() })
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("生字列表 - 即将推出")
        }
    }
}

@Composable
fun WordAddScreen(navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "添加生字", onNavigationClick = { navigationManager.navigateBack() })
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("添加生字 - 即将推出")
        }
    }
}

@Composable
fun WordEditScreen(wordId: String, navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "编辑生字", onNavigationClick = { navigationManager.navigateBack() })
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("编辑生字: $wordId - 即将推出")
        }
    }
}

@Composable
fun WordCategoriesScreen(navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "生字分类", onNavigationClick = { navigationManager.navigateBack() })
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("生字分类 - 即将推出")
        }
    }
}
