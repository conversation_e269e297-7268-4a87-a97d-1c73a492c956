package com.homework.assistant.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.homework.assistant.ui.screens.home.HomeScreen
import com.homework.assistant.ui.screens.dictation.DictationMainScreen
import com.homework.assistant.ui.screens.dictation.DictationSessionScreen
import com.homework.assistant.ui.screens.dictation.DictationResultScreen
import com.homework.assistant.ui.screens.recitation.RecitationMainScreen
import com.homework.assistant.ui.screens.recitation.RecitationSessionScreen
import com.homework.assistant.ui.screens.recitation.RecitationResultScreen
import com.homework.assistant.ui.screens.management.ManagementMainScreen
import com.homework.assistant.ui.screens.management.words.WordListScreen
import com.homework.assistant.ui.screens.management.words.WordAddScreen
import com.homework.assistant.ui.screens.management.words.WordEditScreen
import com.homework.assistant.ui.screens.management.words.WordCategoriesScreen
import com.homework.assistant.ui.screens.management.recitations.RecitationListScreen
import com.homework.assistant.ui.screens.management.recitations.RecitationAddScreen
import com.homework.assistant.ui.screens.management.recitations.RecitationEditScreen
import com.homework.assistant.ui.screens.photocheck.PhotoCheckMainScreen
import com.homework.assistant.ui.screens.photocheck.PhotoCheckCameraScreen
import com.homework.assistant.ui.screens.photocheck.PhotoCheckResultScreen
import com.homework.assistant.ui.screens.settings.SettingsMainScreen
import com.homework.assistant.ui.screens.settings.StatisticsScreen
import com.homework.assistant.ui.screens.settings.PreferencesScreen
import com.homework.assistant.ui.screens.settings.AboutScreen

/**
 * 导航管理器
 * 负责管理应用的导航逻辑
 */
class NavigationManager(
    private val navController: NavHostController
) {

    /**
     * 导航到指定屏幕
     */
    fun navigateTo(route: String) {
        navController.navigate(route)
    }

    /**
     * 返回上一页
     */
    fun navigateBack() {
        navController.popBackStack()
    }

    /**
     * 返回到指定屏幕
     */
    fun navigateBackTo(route: String, inclusive: Boolean = false) {
        navController.popBackStack(route, inclusive)
    }

    /**
     * 清空导航栈并导航到指定屏幕
     */
    fun navigateAndClearStack(route: String) {
        navController.navigate(route) {
            popUpTo(navController.graph.startDestinationRoute ?: Screen.HOME) {
                inclusive = true
            }
        }
    }
}

/**
 * 应用导航组合函数
 */
@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController(),
    startDestination: String = Screen.HOME
) {
    val navigationManager = remember(navController) {
        NavigationManager(navController)
    }

    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // 主页
        composable(Screen.HOME) {
            HomeScreen(navigationManager = navigationManager)
        }

        // 听写生字相关页面
        composable(Screen.Dictation.MAIN) {
            DictationMainScreen(navigationManager = navigationManager)
        }

        composable(Screen.Dictation.SESSION) { backStackEntry ->
            val wordIds = backStackEntry.arguments?.getString("wordIds")?.split(",") ?: emptyList()
            val sessionId = backStackEntry.arguments?.getString("sessionId")
            DictationSessionScreen(
                wordIds = wordIds,
                sessionId = sessionId,
                navigationManager = navigationManager
            )
        }

        composable(Screen.Dictation.RESULT) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
            DictationResultScreen(
                sessionId = sessionId,
                navigationManager = navigationManager
            )
        }

        // 背诵练习相关页面
        composable(Screen.Recitation.MAIN) {
            RecitationMainScreen(navigationManager = navigationManager)
        }

        composable(Screen.Recitation.SESSION) { backStackEntry ->
            val recitationId = backStackEntry.arguments?.getString("recitationId") ?: ""
            val sessionId = backStackEntry.arguments?.getString("sessionId")
            RecitationSessionScreen(
                recitationId = recitationId,
                sessionId = sessionId,
                navigationManager = navigationManager
            )
        }

        composable(Screen.Recitation.RESULT) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
            RecitationResultScreen(
                sessionId = sessionId,
                navigationManager = navigationManager
            )
        }

        // 内容管理相关页面
        composable(Screen.Management.MAIN) {
            ManagementMainScreen(navigationManager = navigationManager)
        }

        // 生字管理
        composable(Screen.Management.Words.LIST) {
            WordListScreen(navigationManager = navigationManager)
        }

        composable(Screen.Management.Words.ADD) {
            WordAddScreen(navigationManager = navigationManager)
        }

        composable(Screen.Management.Words.EDIT) { backStackEntry ->
            val wordId = backStackEntry.arguments?.getString("wordId") ?: ""
            WordEditScreen(
                wordId = wordId,
                navigationManager = navigationManager
            )
        }

        composable(Screen.Management.Words.CATEGORIES) {
            WordCategoriesScreen(navigationManager = navigationManager)
        }

        // 背诵内容管理
        composable(Screen.Management.Recitations.LIST) {
            RecitationListScreen(navigationManager = navigationManager)
        }

        composable(Screen.Management.Recitations.ADD) {
            RecitationAddScreen(navigationManager = navigationManager)
        }

        composable(Screen.Management.Recitations.EDIT) { backStackEntry ->
            val recitationId = backStackEntry.arguments?.getString("recitationId") ?: ""
            RecitationEditScreen(
                recitationId = recitationId,
                navigationManager = navigationManager
            )
        }

        // 拍照检查相关页面
        composable(Screen.PhotoCheck.MAIN) {
            PhotoCheckMainScreen(navigationManager = navigationManager)
        }

        composable(Screen.PhotoCheck.CAMERA) {
            PhotoCheckCameraScreen(navigationManager = navigationManager)
        }

        composable(Screen.PhotoCheck.RESULT) { backStackEntry ->
            val imageUri = backStackEntry.arguments?.getString("imageUri") ?: ""
            val checkId = backStackEntry.arguments?.getString("checkId") ?: ""
            PhotoCheckResultScreen(
                imageUri = imageUri,
                checkId = checkId,
                navigationManager = navigationManager
            )
        }

        // 设置和个人中心
        composable(Screen.Settings.MAIN) {
            SettingsMainScreen(navigationManager = navigationManager)
        }

        composable(Screen.Settings.STATISTICS) {
            StatisticsScreen(navigationManager = navigationManager)
        }

        composable(Screen.Settings.PREFERENCES) {
            PreferencesScreen(navigationManager = navigationManager)
        }

        composable(Screen.Settings.ABOUT) {
            AboutScreen(navigationManager = navigationManager)
        }
    }
}
