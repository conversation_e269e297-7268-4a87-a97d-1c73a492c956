package com.homework.assistant.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.toRoute
import com.homework.assistant.ui.screens.home.HomeScreen
import com.homework.assistant.ui.screens.dictation.DictationMainScreen
import com.homework.assistant.ui.screens.dictation.DictationSessionScreen
import com.homework.assistant.ui.screens.dictation.DictationResultScreen
import com.homework.assistant.ui.screens.recitation.RecitationMainScreen
import com.homework.assistant.ui.screens.recitation.RecitationSessionScreen
import com.homework.assistant.ui.screens.recitation.RecitationResultScreen
import com.homework.assistant.ui.screens.management.ManagementMainScreen
import com.homework.assistant.ui.screens.management.words.WordListScreen
import com.homework.assistant.ui.screens.management.words.WordAddScreen
import com.homework.assistant.ui.screens.management.words.WordEditScreen
import com.homework.assistant.ui.screens.management.words.WordCategoriesScreen
import com.homework.assistant.ui.screens.management.recitations.RecitationListScreen
import com.homework.assistant.ui.screens.management.recitations.RecitationAddScreen
import com.homework.assistant.ui.screens.management.recitations.RecitationEditScreen
import com.homework.assistant.ui.screens.photocheck.PhotoCheckMainScreen
import com.homework.assistant.ui.screens.photocheck.PhotoCheckCameraScreen
import com.homework.assistant.ui.screens.photocheck.PhotoCheckResultScreen
import com.homework.assistant.ui.screens.settings.SettingsMainScreen
import com.homework.assistant.ui.screens.settings.StatisticsScreen
import com.homework.assistant.ui.screens.settings.PreferencesScreen
import com.homework.assistant.ui.screens.settings.AboutScreen

/**
 * 导航管理器
 * 负责管理应用的导航逻辑
 */
class NavigationManager(
    private val navController: NavHostController
) {
    
    /**
     * 导航到指定屏幕
     */
    fun navigateTo(screen: Screen) {
        navController.navigate(screen)
    }
    
    /**
     * 返回上一页
     */
    fun navigateBack() {
        navController.popBackStack()
    }
    
    /**
     * 返回到指定屏幕
     */
    fun navigateBackTo(screen: Screen, inclusive: Boolean = false) {
        navController.popBackStack(screen, inclusive)
    }
    
    /**
     * 清空导航栈并导航到指定屏幕
     */
    fun navigateAndClearStack(screen: Screen) {
        navController.navigate(screen) {
            popUpTo(navController.graph.startDestinationId) {
                inclusive = true
            }
        }
    }
}

/**
 * 应用导航组合函数
 */
@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController(),
    startDestination: Screen = Screen.Home
) {
    val navigationManager = remember(navController) {
        NavigationManager(navController)
    }
    
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // 主页
        composable<Screen.Home> {
            HomeScreen(navigationManager = navigationManager)
        }
        
        // 听写生字相关页面
        composable<Screen.Dictation.Main> {
            DictationMainScreen(navigationManager = navigationManager)
        }
        
        composable<Screen.Dictation.Session> { backStackEntry ->
            val session = backStackEntry.toRoute<Screen.Dictation.Session>()
            DictationSessionScreen(
                wordIds = session.wordIds,
                sessionId = session.sessionId,
                navigationManager = navigationManager
            )
        }
        
        composable<Screen.Dictation.Result> { backStackEntry ->
            val result = backStackEntry.toRoute<Screen.Dictation.Result>()
            DictationResultScreen(
                sessionId = result.sessionId,
                navigationManager = navigationManager
            )
        }
        
        // 背诵练习相关页面
        composable<Screen.Recitation.Main> {
            RecitationMainScreen(navigationManager = navigationManager)
        }
        
        composable<Screen.Recitation.Session> { backStackEntry ->
            val session = backStackEntry.toRoute<Screen.Recitation.Session>()
            RecitationSessionScreen(
                recitationId = session.recitationId,
                sessionId = session.sessionId,
                navigationManager = navigationManager
            )
        }
        
        composable<Screen.Recitation.Result> { backStackEntry ->
            val result = backStackEntry.toRoute<Screen.Recitation.Result>()
            RecitationResultScreen(
                sessionId = result.sessionId,
                navigationManager = navigationManager
            )
        }
        
        // 内容管理相关页面
        composable<Screen.Management.Main> {
            ManagementMainScreen(navigationManager = navigationManager)
        }
        
        // 生字管理
        composable<Screen.Management.Words.List> {
            WordListScreen(navigationManager = navigationManager)
        }
        
        composable<Screen.Management.Words.Add> {
            WordAddScreen(navigationManager = navigationManager)
        }
        
        composable<Screen.Management.Words.Edit> { backStackEntry ->
            val edit = backStackEntry.toRoute<Screen.Management.Words.Edit>()
            WordEditScreen(
                wordId = edit.wordId,
                navigationManager = navigationManager
            )
        }
        
        composable<Screen.Management.Words.Categories> {
            WordCategoriesScreen(navigationManager = navigationManager)
        }
        
        // 背诵内容管理
        composable<Screen.Management.Recitations.List> {
            RecitationListScreen(navigationManager = navigationManager)
        }
        
        composable<Screen.Management.Recitations.Add> {
            RecitationAddScreen(navigationManager = navigationManager)
        }
        
        composable<Screen.Management.Recitations.Edit> { backStackEntry ->
            val edit = backStackEntry.toRoute<Screen.Management.Recitations.Edit>()
            RecitationEditScreen(
                recitationId = edit.recitationId,
                navigationManager = navigationManager
            )
        }
        
        // 拍照检查相关页面
        composable<Screen.PhotoCheck.Main> {
            PhotoCheckMainScreen(navigationManager = navigationManager)
        }
        
        composable<Screen.PhotoCheck.Camera> {
            PhotoCheckCameraScreen(navigationManager = navigationManager)
        }
        
        composable<Screen.PhotoCheck.Result> { backStackEntry ->
            val result = backStackEntry.toRoute<Screen.PhotoCheck.Result>()
            PhotoCheckResultScreen(
                imageUri = result.imageUri,
                checkId = result.checkId,
                navigationManager = navigationManager
            )
        }
        
        // 设置和个人中心
        composable<Screen.Settings.Main> {
            SettingsMainScreen(navigationManager = navigationManager)
        }
        
        composable<Screen.Settings.Statistics> {
            StatisticsScreen(navigationManager = navigationManager)
        }
        
        composable<Screen.Settings.Preferences> {
            PreferencesScreen(navigationManager = navigationManager)
        }
        
        composable<Screen.Settings.About> {
            AboutScreen(navigationManager = navigationManager)
        }
    }
}
