package com.homework.assistant.ui.navigation

/**
 * 应用导航屏幕定义
 * 使用字符串路由进行导航
 */
object Screen {

    const val HOME = "home"

    // 听写生字相关页面
    object Dictation {
        const val MAIN = "dictation_main"
        const val SESSION = "dictation_session"
        const val RESULT = "dictation_result/{sessionId}"

        fun sessionRoute(wordIds: List<String>, sessionId: String? = null): String {
            return if (sessionId != null) {
                "dictation_session?wordIds=${wordIds.joinToString(",")}&sessionId=$sessionId"
            } else {
                "dictation_session?wordIds=${wordIds.joinToString(",")}"
            }
        }

        fun resultRoute(sessionId: String): String {
            return "dictation_result/$sessionId"
        }
    }

    // 背诵练习相关页面
    object Recitation {
        const val MAIN = "recitation_main"
        const val SESSION = "recitation_session"
        const val RESULT = "recitation_result/{sessionId}"

        fun sessionRoute(recitationId: String, sessionId: String? = null): String {
            return if (sessionId != null) {
                "recitation_session?recitationId=$recitationId&sessionId=$sessionId"
            } else {
                "recitation_session?recitationId=$recitationId"
            }
        }

        fun resultRoute(sessionId: String): String {
            return "recitation_result/$sessionId"
        }
    }

    // 内容管理相关页面
    object Management {
        const val MAIN = "management_main"

        object Words {
            const val LIST = "words_list"
            const val ADD = "words_add"
            const val EDIT = "words_edit/{wordId}"
            const val CATEGORIES = "words_categories"

            fun editRoute(wordId: String): String {
                return "words_edit/$wordId"
            }
        }

        object Recitations {
            const val LIST = "recitations_list"
            const val ADD = "recitations_add"
            const val EDIT = "recitations_edit/{recitationId}"

            fun editRoute(recitationId: String): String {
                return "recitations_edit/$recitationId"
            }
        }
    }

    // 拍照检查相关页面
    object PhotoCheck {
        const val MAIN = "photocheck_main"
        const val CAMERA = "photocheck_camera"
        const val RESULT = "photocheck_result"

        fun resultRoute(imageUri: String, checkId: String): String {
            return "photocheck_result?imageUri=$imageUri&checkId=$checkId"
        }
    }

    // 设置和个人中心
    object Settings {
        const val MAIN = "settings_main"
        const val STATISTICS = "settings_statistics"
        const val PREFERENCES = "settings_preferences"
        const val ABOUT = "settings_about"
    }
}
