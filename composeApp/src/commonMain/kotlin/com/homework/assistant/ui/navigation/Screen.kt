package com.homework.assistant.ui.navigation

import kotlinx.serialization.Serializable

/**
 * 应用导航屏幕定义
 * 使用 Kotlin Serialization 支持类型安全的导航
 */
@Serializable
sealed class Screen {
    
    /**
     * 主页面
     */
    @Serializable
    data object Home : Screen()
    
    /**
     * 听写生字相关页面
     */
    @Serializable
    sealed class Dictation : Screen() {
        
        /**
         * 听写主页 - 选择生字列表
         */
        @Serializable
        data object Main : Dictation()
        
        /**
         * 听写进行中页面
         * @param wordIds 要听写的生字ID列表
         * @param sessionId 会话ID（可选，用于恢复会话）
         */
        @Serializable
        data class Session(
            val wordIds: List<String>,
            val sessionId: String? = null
        ) : Dictation()
        
        /**
         * 听写结果页面
         * @param sessionId 会话ID
         */
        @Serializable
        data class Result(val sessionId: String) : Dictation()
    }
    
    /**
     * 背诵练习相关页面
     */
    @Serializable
    sealed class Recitation : Screen() {
        
        /**
         * 背诵主页 - 选择背诵内容
         */
        @Serializable
        data object Main : Recitation()
        
        /**
         * 背诵进行中页面
         * @param recitationId 背诵内容ID
         * @param sessionId 会话ID（可选，用于恢复会话）
         */
        @Serializable
        data class Session(
            val recitationId: String,
            val sessionId: String? = null
        ) : Recitation()
        
        /**
         * 背诵结果页面
         * @param sessionId 会话ID
         */
        @Serializable
        data class Result(val sessionId: String) : Recitation()
    }
    
    /**
     * 内容管理相关页面
     */
    @Serializable
    sealed class Management : Screen() {
        
        /**
         * 内容管理主页
         */
        @Serializable
        data object Main : Management()
        
        /**
         * 生字管理
         */
        @Serializable
        sealed class Words : Management() {
            
            /**
             * 生字列表
             */
            @Serializable
            data object List : Words()
            
            /**
             * 添加生字
             */
            @Serializable
            data object Add : Words()
            
            /**
             * 编辑生字
             * @param wordId 生字ID
             */
            @Serializable
            data class Edit(val wordId: String) : Words()
            
            /**
             * 生字分类管理
             */
            @Serializable
            data object Categories : Words()
        }
        
        /**
         * 背诵内容管理
         */
        @Serializable
        sealed class Recitations : Management() {
            
            /**
             * 背诵内容列表
             */
            @Serializable
            data object List : Recitations()
            
            /**
             * 添加背诵内容
             */
            @Serializable
            data object Add : Recitations()
            
            /**
             * 编辑背诵内容
             * @param recitationId 背诵内容ID
             */
            @Serializable
            data class Edit(val recitationId: String) : Recitations()
        }
    }
    
    /**
     * 拍照检查相关页面
     */
    @Serializable
    sealed class PhotoCheck : Screen() {
        
        /**
         * 拍照检查主页
         */
        @Serializable
        data object Main : PhotoCheck()
        
        /**
         * 拍照界面
         */
        @Serializable
        data object Camera : PhotoCheck()
        
        /**
         * 检查结果页面
         * @param imageUri 图片URI
         * @param checkId 检查ID
         */
        @Serializable
        data class Result(
            val imageUri: String,
            val checkId: String
        ) : PhotoCheck()
    }
    
    /**
     * 设置和个人中心
     */
    @Serializable
    sealed class Settings : Screen() {
        
        /**
         * 设置主页
         */
        @Serializable
        data object Main : Settings()
        
        /**
         * 学习统计
         */
        @Serializable
        data object Statistics : Settings()
        
        /**
         * 应用设置
         */
        @Serializable
        data object Preferences : Settings()
        
        /**
         * 关于页面
         */
        @Serializable
        data object About : Settings()
    }
}
