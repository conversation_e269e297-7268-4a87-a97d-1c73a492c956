package com.homework.assistant.ui.screens.recitation

import androidx.lifecycle.viewModelScope
import com.homework.assistant.shared.data.model.Recitation
import com.homework.assistant.shared.recitation.RecitationService
import com.homework.assistant.shared.recitation.RecitationSession
import com.homework.assistant.shared.recitation.RecitationState
import com.homework.assistant.shared.recitation.RecitationSettings
import com.homework.assistant.shared.recitation.RecitationAttempt
import com.homework.assistant.shared.recitation.RecitationProgress
import com.homework.assistant.shared.recitation.RecitationSessionState
import com.homework.assistant.ui.base.BaseViewModel
import com.homework.assistant.ui.base.UiState
import com.homework.assistant.ui.base.UiEvent
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 背诵ViewModel
 */
class RecitationViewModel(
    private val recitationService: RecitationService
) : BaseViewModel<RecitationUiState, RecitationUiEvent>() {
    
    override val _uiState = MutableStateFlow(
        RecitationUiState(
            session = null,
            currentSentence = null,
            progress = RecitationProgress(0, 0, 0, 0, 0, 0, 0, 0),
            isPrompting = false,
            isListening = false,
            lastAttempt = null,
            userInput = "",
            showHint = false,
            currentHint = "",
            availableCommands = emptyList()
        )
    )
    
    init {
        // 监听背诵状态变化
        viewModelScope.launch {
            recitationService.getRecitationState().collectLatest { recitationState ->
                updateState { currentState ->
                    currentState.copy(
                        session = recitationState.session,
                        currentSentence = recitationState.currentSentence,
                        progress = recitationState.progress,
                        isPrompting = recitationState.isPrompting,
                        isListening = recitationState.isListening,
                        lastAttempt = recitationState.lastAttempt,
                        availableCommands = recitationState.availableCommands
                    )
                }
            }
        }
    }
    
    override fun handleEvent(event: RecitationUiEvent) {
        when (event) {
            is RecitationUiEvent.StartRecitation -> startRecitation(event.recitation, event.settings)
            is RecitationUiEvent.SpeakCurrentSentence -> speakCurrentSentence()
            is RecitationUiEvent.RepeatSentence -> repeatSentence()
            is RecitationUiEvent.SpeakFullPoem -> speakFullPoem()
            is RecitationUiEvent.GiveHint -> giveHint()
            is RecitationUiEvent.HideHint -> hideHint()
            is RecitationUiEvent.NextSentence -> nextSentence()
            is RecitationUiEvent.PreviousSentence -> previousSentence()
            is RecitationUiEvent.UpdateUserInput -> updateUserInput(event.input)
            is RecitationUiEvent.SubmitRecitation -> submitRecitation()
            is RecitationUiEvent.PauseRecitation -> pauseRecitation()
            is RecitationUiEvent.ResumeRecitation -> resumeRecitation()
            is RecitationUiEvent.FinishRecitation -> finishRecitation()
            is RecitationUiEvent.SkipSentence -> skipSentence()
            is RecitationUiEvent.RestartRecitation -> restartRecitation()
            is RecitationUiEvent.HandleVoiceCommand -> handleVoiceCommand(event.command)
        }
    }
    
    private fun startRecitation(recitation: Recitation, settings: RecitationSettings) {
        launchSafely(showLoading = true) {
            val session = recitationService.startRecitation(recitation, settings)
            // 状态会通过Flow自动更新
        }
    }
    
    private fun speakCurrentSentence() {
        launchSafely {
            recitationService.speakCurrentSentence()
        }
    }
    
    private fun repeatSentence() {
        launchSafely {
            recitationService.repeatCurrentSentence()
        }
    }
    
    private fun speakFullPoem() {
        launchSafely {
            recitationService.speakFullPoem()
        }
    }
    
    private fun giveHint() {
        launchSafely {
            val result = recitationService.giveHint()
            if (result.isSuccess) {
                updateState { it.copy(showHint = true, currentHint = result.getOrNull() ?: "") }
            }
        }
    }
    
    private fun hideHint() {
        updateState { it.copy(showHint = false, currentHint = "") }
    }
    
    private fun nextSentence() {
        launchSafely {
            recitationService.nextSentence()
            // 清空用户输入
            updateState { it.copy(userInput = "") }
        }
    }
    
    private fun previousSentence() {
        launchSafely {
            recitationService.previousSentence()
            // 清空用户输入
            updateState { it.copy(userInput = "") }
        }
    }
    
    private fun updateUserInput(input: String) {
        updateState { it.copy(userInput = input) }
    }
    
    private fun submitRecitation() {
        val currentInput = _uiState.value.userInput
        if (currentInput.isBlank()) return
        
        launchSafely {
            val result = recitationService.submitRecitation(currentInput)
            if (result.isSuccess) {
                // 清空用户输入
                updateState { it.copy(userInput = "") }
            }
        }
    }
    
    private fun pauseRecitation() {
        launchSafely {
            recitationService.pauseRecitation()
        }
    }
    
    private fun resumeRecitation() {
        launchSafely {
            recitationService.resumeRecitation()
        }
    }
    
    private fun finishRecitation() {
        launchSafely {
            recitationService.finishRecitation()
        }
    }
    
    private fun skipSentence() {
        launchSafely {
            recitationService.skipCurrentSentence()
            // 清空用户输入
            updateState { it.copy(userInput = "") }
        }
    }
    
    private fun restartRecitation() {
        launchSafely {
            recitationService.restartRecitation()
            // 清空用户输入和提示
            updateState { it.copy(userInput = "", showHint = false, currentHint = "") }
        }
    }
    
    private fun handleVoiceCommand(command: String) {
        launchSafely {
            recitationService.handleVoiceCommand(command)
        }
    }
}

/**
 * 背诵UI状态
 */
data class RecitationUiState(
    val session: RecitationSession?,
    val currentSentence: String?,
    val progress: RecitationProgress,
    val isPrompting: Boolean,
    val isListening: Boolean,
    val lastAttempt: RecitationAttempt?,
    val userInput: String,
    val showHint: Boolean,
    val currentHint: String,
    val availableCommands: List<String>
) : UiState {
    
    val canSubmit: Boolean
        get() = userInput.isNotBlank() && isListening
    
    val isSessionActive: Boolean
        get() = session != null && session.state != RecitationSessionState.COMPLETED && session.state != RecitationSessionState.CANCELLED
    
    val canNavigate: Boolean
        get() = session != null && !isPrompting
    
    val canUseHint: Boolean
        get() = session?.settings?.enableHints == true && 
                progress.hintsUsed < (session?.settings?.maxHints ?: 0)
}

/**
 * 背诵UI事件
 */
sealed class RecitationUiEvent : UiEvent {
    data class StartRecitation(val recitation: Recitation, val settings: RecitationSettings = RecitationSettings()) : RecitationUiEvent()
    data object SpeakCurrentSentence : RecitationUiEvent()
    data object RepeatSentence : RecitationUiEvent()
    data object SpeakFullPoem : RecitationUiEvent()
    data object GiveHint : RecitationUiEvent()
    data object HideHint : RecitationUiEvent()
    data object NextSentence : RecitationUiEvent()
    data object PreviousSentence : RecitationUiEvent()
    data class UpdateUserInput(val input: String) : RecitationUiEvent()
    data object SubmitRecitation : RecitationUiEvent()
    data object PauseRecitation : RecitationUiEvent()
    data object ResumeRecitation : RecitationUiEvent()
    data object FinishRecitation : RecitationUiEvent()
    data object SkipSentence : RecitationUiEvent()
    data object RestartRecitation : RecitationUiEvent()
    data class HandleVoiceCommand(val command: String) : RecitationUiEvent()
}
