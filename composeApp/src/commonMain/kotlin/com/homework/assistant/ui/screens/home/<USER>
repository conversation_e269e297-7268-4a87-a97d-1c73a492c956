package com.homework.assistant.ui.screens.home

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.components.FeatureCard
import com.homework.assistant.ui.navigation.NavigationManager
import com.homework.assistant.ui.navigation.Screen

/**
 * 主页面
 */
@Composable
fun HomeScreen(
    navigationManager: NavigationManager
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部栏
        AppTopBar(
            title = "小学生作业助手",
            actions = {
                IconButton(
                    onClick = {
                        navigationManager.navigateTo(Screen.Settings.MAIN)
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "设置"
                    )
                }
            }
        )
        
        // 主要内容
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 欢迎信息
            WelcomeSection()
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 功能网格
            FeatureGrid(navigationManager = navigationManager)
        }
    }
}

/**
 * 欢迎区域
 */
@Composable
private fun WelcomeSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        shape = MaterialTheme.shapes.large
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "🎓",
                style = MaterialTheme.typography.displayMedium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "欢迎使用作业助手",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "让学习变得更有趣！",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 功能网格
 */
@Composable
private fun FeatureGrid(
    navigationManager: NavigationManager
) {
    val features = remember {
        listOf(
            FeatureItem(
                title = "听写生字",
                description = "语音播报，智能识别",
                icon = "📝",
                route = Screen.Dictation.MAIN
            ),
            FeatureItem(
                title = "背诵练习",
                description = "智能提示，完成检测",
                icon = "📖",
                route = Screen.Recitation.MAIN
            ),
            FeatureItem(
                title = "拍照检查",
                description = "OCR识别，智能批改",
                icon = "📷",
                route = Screen.PhotoCheck.MAIN
            ),
            FeatureItem(
                title = "内容管理",
                description = "添加生字，管理内容",
                icon = "📚",
                route = Screen.Management.MAIN
            )
        )
    }
    
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(features) { feature ->
            FeatureCard(
                title = feature.title,
                description = feature.description,
                icon = feature.icon,
                onClick = {
                    navigationManager.navigateTo(feature.route)
                }
            )
        }
    }
}

/**
 * 功能项数据类
 */
private data class FeatureItem(
    val title: String,
    val description: String,
    val icon: String,
    val route: String
)
