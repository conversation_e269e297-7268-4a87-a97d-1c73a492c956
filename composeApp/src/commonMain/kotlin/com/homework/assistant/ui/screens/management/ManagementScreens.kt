package com.homework.assistant.ui.screens.management

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.components.FeatureCard
import com.homework.assistant.ui.navigation.NavigationManager
import com.homework.assistant.ui.navigation.Screen

/**
 * 内容管理主页面
 */
@Composable
fun ManagementMainScreen(
    navigationManager: NavigationManager
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "内容管理",
            onNavigationClick = {
                navigationManager.navigateBack()
            }
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 欢迎信息
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "📚",
                        style = MaterialTheme.typography.displayMedium
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    Text(
                        text = "内容管理中心",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "管理生字和背诵内容",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 管理功能网格
            ManagementFeatureGrid(navigationManager = navigationManager)
        }
    }
}

/**
 * 管理功能网格
 */
@Composable
private fun ManagementFeatureGrid(
    navigationManager: NavigationManager
) {
    val features = remember {
        listOf(
            ManagementFeatureItem(
                title = "生字管理",
                description = "添加、编辑、删除生字",
                icon = "📝",
                route = Screen.Management.Words.LIST
            ),
            ManagementFeatureItem(
                title = "生字分类",
                description = "管理生字分类和标签",
                icon = "🏷️",
                route = Screen.Management.Words.CATEGORIES
            ),
            ManagementFeatureItem(
                title = "背诵内容",
                description = "管理诗词和背诵材料",
                icon = "📖",
                route = Screen.Management.Recitations.LIST
            ),
            ManagementFeatureItem(
                title = "内容分类",
                description = "管理背诵内容分类",
                icon = "📂",
                route = Screen.Management.Recitations.LIST // 暂时指向同一个页面
            )
        )
    }

    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(features) { feature ->
            FeatureCard(
                title = feature.title,
                description = feature.description,
                icon = feature.icon,
                onClick = {
                    navigationManager.navigateTo(feature.route)
                }
            )
        }
    }
}

/**
 * 管理功能项数据类
 */
private data class ManagementFeatureItem(
    val title: String,
    val description: String,
    val icon: String,
    val route: String
)
