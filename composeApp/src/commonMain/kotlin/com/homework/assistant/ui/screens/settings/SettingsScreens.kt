package com.homework.assistant.ui.screens.settings

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.navigation.NavigationManager

@Composable
fun SettingsMainScreen(navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "设置", onNavigationClick = { navigationManager.navigateBack() })
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "⚙️",
                style = MaterialTheme.typography.displayLarge
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "设置功能",
                style = MaterialTheme.typography.headlineMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "即将推出...",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun StatisticsScreen(navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "学习统计", onNavigationClick = { navigationManager.navigateBack() })
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("学习统计 - 即将推出")
        }
    }
}

@Composable
fun PreferencesScreen(navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "应用设置", onNavigationClick = { navigationManager.navigateBack() })
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("应用设置 - 即将推出")
        }
    }
}

@Composable
fun AboutScreen(navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "关于", onNavigationClick = { navigationManager.navigateBack() })
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "🎓",
                style = MaterialTheme.typography.displayLarge
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "小学生作业助手",
                style = MaterialTheme.typography.headlineMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "版本 1.0.0",
                style = MaterialTheme.typography.bodyLarge
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "基于 Compose Multiplatform 开发",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = "支持 Android • iOS • 鸿蒙 Next",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
