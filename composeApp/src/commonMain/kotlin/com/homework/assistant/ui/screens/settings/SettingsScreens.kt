package com.homework.assistant.ui.screens.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.homework.assistant.shared.data.model.LearningStatistics
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.components.FeatureCard
import com.homework.assistant.ui.navigation.NavigationManager
import com.homework.assistant.ui.navigation.Screen

/**
 * 设置主页面
 */
@Composable
fun SettingsMainScreen(navigationManager: NavigationManager) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "设置",
            onNavigationClick = { navigationManager.navigateBack() }
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 欢迎信息
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "⚙️",
                        style = MaterialTheme.typography.displayMedium
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    Text(
                        text = "设置中心",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "个性化你的学习体验",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 设置功能网格
            SettingsFeatureGrid(navigationManager = navigationManager)
        }
    }
}

/**
 * 设置功能网格
 */
@Composable
private fun SettingsFeatureGrid(
    navigationManager: NavigationManager
) {
    val features = remember {
        listOf(
            SettingsFeatureItem(
                title = "学习统计",
                description = "查看学习进度和成绩",
                icon = "📊",
                route = Screen.Settings.STATISTICS
            ),
            SettingsFeatureItem(
                title = "应用设置",
                description = "个性化设置和偏好",
                icon = "🔧",
                route = Screen.Settings.PREFERENCES
            ),
            SettingsFeatureItem(
                title = "关于应用",
                description = "版本信息和帮助",
                icon = "ℹ️",
                route = Screen.Settings.ABOUT
            ),
            SettingsFeatureItem(
                title = "数据管理",
                description = "备份和恢复数据",
                icon = "💾",
                route = Screen.Settings.PREFERENCES // 暂时指向设置页面
            )
        )
    }

    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(features) { feature ->
            FeatureCard(
                title = feature.title,
                description = feature.description,
                icon = feature.icon,
                onClick = {
                    navigationManager.navigateTo(feature.route)
                }
            )
        }
    }
}

/**
 * 学习统计页面
 */
@Composable
fun StatisticsScreen(navigationManager: NavigationManager) {
    // 模拟数据 - 实际应该从ViewModel获取
    val mockStatistics = remember {
        LearningStatistics(
            totalWords = 156,
            masteredWords = 89,
            totalRecitations = 23,
            masteredRecitations = 15,
            totalStudyTime = 1250, // 分钟
            streakDays = 7,
            averageAccuracy = 0.85f,
            weeklyProgress = listOf(0.6f, 0.7f, 0.8f, 0.75f, 0.9f, 0.85f, 0.88f)
        )
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "学习统计",
            onNavigationClick = { navigationManager.navigateBack() }
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 总体统计
            item {
                OverallStatisticsCard(statistics = mockStatistics)
            }

            // 学习进度
            item {
                LearningProgressCard(statistics = mockStatistics)
            }

            // 本周表现
            item {
                WeeklyPerformanceCard(statistics = mockStatistics)
            }

            // 成就徽章
            item {
                AchievementCard(statistics = mockStatistics)
            }
        }
    }
}

/**
 * 应用设置页面
 */
@Composable
fun PreferencesScreen(navigationManager: NavigationManager) {
    // 模拟状态 - 实际应该从ViewModel获取
    var isDarkTheme by remember { mutableStateOf(false) }
    var isNotificationEnabled by remember { mutableStateOf(true) }
    var voiceSpeed by remember { mutableFloatStateOf(1.0f) }
    var autoBackup by remember { mutableStateOf(true) }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "应用设置",
            onNavigationClick = { navigationManager.navigateBack() }
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 外观设置
            item {
                AppearanceSettingsCard(
                    isDarkTheme = isDarkTheme,
                    onThemeChange = { isDarkTheme = it }
                )
            }

            // 语音设置
            item {
                VoiceSettingsCard(
                    voiceSpeed = voiceSpeed,
                    onSpeedChange = { voiceSpeed = it }
                )
            }

            // 通知设置
            item {
                NotificationSettingsCard(
                    isEnabled = isNotificationEnabled,
                    onEnabledChange = { isNotificationEnabled = it }
                )
            }

            // 数据设置
            item {
                DataSettingsCard(
                    autoBackup = autoBackup,
                    onAutoBackupChange = { autoBackup = it },
                    onClearCache = {
                        // TODO: 清除缓存
                    },
                    onExportData = {
                        // TODO: 导出数据
                    },
                    onImportData = {
                        // TODO: 导入数据
                    }
                )
            }

            // 重置设置
            item {
                ResetSettingsCard(
                    onReset = {
                        // TODO: 重置设置
                        isDarkTheme = false
                        isNotificationEnabled = true
                        voiceSpeed = 1.0f
                        autoBackup = true
                    }
                )
            }
        }
    }
}

/**
 * 关于页面
 */
@Composable
fun AboutScreen(navigationManager: NavigationManager) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        AppTopBar(
            title = "关于",
            onNavigationClick = { navigationManager.navigateBack() }
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 应用信息
            item {
                AppInfoCard()
            }

            // 功能介绍
            item {
                FeatureIntroCard()
            }

            // 技术信息
            item {
                TechInfoCard()
            }

            // 联系方式
            item {
                ContactCard()
            }
        }
    }
}

/**
 * 设置功能项数据类
 */
private data class SettingsFeatureItem(
    val title: String,
    val description: String,
    val icon: String,
    val route: String
)
