package com.homework.assistant.ui.screens.recitation

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.homework.assistant.ui.theme.CustomTextStyles

/**
 * 背诵进度卡片
 */
@Composable
fun RecitationProgressCard(
    currentIndex: Int,
    totalCount: Int,
    correctCount: Int,
    wrongCount: Int,
    title: String,
    author: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 诗词信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "作者：$author",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Text(
                    text = "${currentIndex + 1} / $totalCount",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            LinearProgressIndicator(
                progress = { if (totalCount > 0) (currentIndex + 1).toFloat() / totalCount else 0f },
                modifier = Modifier.fillMaxWidth(),
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "✅",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = "$correctCount",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "❌",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = "$wrongCount",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "📊",
                        style = MaterialTheme.typography.titleMedium
                    )
                    val accuracy = if (correctCount + wrongCount > 0) {
                        (correctCount * 100) / (correctCount + wrongCount)
                    } else 0
                    Text(
                        text = "$accuracy%",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 当前句子显示组件
 */
@Composable
fun CurrentSentenceDisplay(
    sentence: String,
    showHint: Boolean,
    currentHint: String,
    isPrompting: Boolean,
    onToggleHint: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 播放状态指示
            if (isPrompting) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "正在播放提示...",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // 句子显示
            Text(
                text = sentence,
                style = CustomTextStyles.PoemLine,
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 提示显示/切换按钮
            if (showHint) {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    ),
                    modifier = Modifier.clickable { onToggleHint() }
                ) {
                    Text(
                        text = currentHint,
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.padding(12.dp),
                        textAlign = TextAlign.Center
                    )
                }
            } else {
                OutlinedButton(
                    onClick = onToggleHint,
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("获取提示")
                }
            }
        }
    }
}

/**
 * 背诵输入区域
 */
@Composable
fun RecitationInputSection(
    userInput: String,
    onInputChange: (String) -> Unit,
    onSubmit: () -> Unit,
    enabled: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "请背诵这句诗：",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedTextField(
                    value = userInput,
                    onValueChange = onInputChange,
                    modifier = Modifier.weight(1f),
                    placeholder = { Text("在这里输入背诵内容...") },
                    enabled = enabled,
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = { if (userInput.isNotBlank()) onSubmit() }
                    )
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Button(
                    onClick = onSubmit,
                    enabled = enabled && userInput.isNotBlank()
                ) {
                    Text("提交")
                }
            }
        }
    }
}

/**
 * 背诵控制按钮
 */
@Composable
fun RecitationControlButtons(
    onSpeak: () -> Unit,
    onRepeat: () -> Unit,
    onFullPoem: () -> Unit,
    onPrevious: () -> Unit,
    onNext: () -> Unit,
    onSkip: () -> Unit,
    onFinish: () -> Unit,
    canPrevious: Boolean,
    canNext: Boolean,
    canSkip: Boolean,
    isPrompting: Boolean
) {
    Column {
        // 语音控制按钮
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "语音控制",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    ControlButton(
                        icon = Icons.Default.PlayArrow,
                        text = "播放",
                        onClick = onSpeak,
                        enabled = !isPrompting
                    )
                    
                    ControlButton(
                        icon = Icons.Default.Refresh,
                        text = "重复",
                        onClick = onRepeat,
                        enabled = !isPrompting
                    )
                    
                    ControlButton(
                        icon = Icons.Default.VolumeUp,
                        text = "全诗",
                        onClick = onFullPoem,
                        enabled = !isPrompting
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 导航控制按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            OutlinedButton(
                onClick = onPrevious,
                enabled = canPrevious && !isPrompting
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = null
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("上一句")
            }
            
            OutlinedButton(
                onClick = onSkip,
                enabled = canSkip && !isPrompting
            ) {
                Text("跳过")
            }
            
            OutlinedButton(
                onClick = onNext,
                enabled = canNext && !isPrompting
            ) {
                Text("下一句")
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    imageVector = Icons.Default.ArrowForward,
                    contentDescription = null
                )
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 完成按钮
        Button(
            onClick = onFinish,
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.error
            )
        ) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("完成背诵")
        }
    }
}

/**
 * 控制按钮组件
 */
@Composable
private fun ControlButton(
    icon: ImageVector,
    text: String,
    onClick: () -> Unit,
    enabled: Boolean = true
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(56.dp)
                .clip(CircleShape)
                .background(
                    if (enabled) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.surfaceVariant
                    }
                )
                .clickable(enabled = enabled) { onClick() },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text,
                tint = if (enabled) {
                    MaterialTheme.colorScheme.onPrimary
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                },
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall,
            color = if (enabled) {
                MaterialTheme.colorScheme.onSurface
            } else {
                MaterialTheme.colorScheme.onSurfaceVariant
            }
        )
    }
}

/**
 * 背诵总结卡片
 */
@Composable
fun RecitationSummaryCard(
    totalScore: Int,
    correctCount: Int,
    totalCount: Int,
    totalTime: Long,
    hintsUsed: Int,
    title: String,
    author: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when {
                totalScore >= 90 -> MaterialTheme.colorScheme.primaryContainer
                totalScore >= 70 -> MaterialTheme.colorScheme.secondaryContainer
                else -> MaterialTheme.colorScheme.errorContainer
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 成绩图标
            Text(
                text = when {
                    totalScore >= 90 -> "🏆"
                    totalScore >= 70 -> "👍"
                    else -> "💪"
                },
                style = MaterialTheme.typography.displayMedium
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 诗词信息
            Text(
                text = title,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )

            Text(
                text = "作者：$author",
                style = MaterialTheme.typography.bodyLarge
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 总分
            Text(
                text = "总分: $totalScore 分",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 正确率
            val accuracy = if (totalCount > 0) (correctCount * 100) / totalCount else 0
            Text(
                text = "正确率: $accuracy% ($correctCount/$totalCount)",
                style = MaterialTheme.typography.titleMedium
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 用时
            val minutes = totalTime / 60000
            val seconds = (totalTime % 60000) / 1000
            Text(
                text = "用时: ${minutes}分${seconds}秒",
                style = MaterialTheme.typography.bodyLarge
            )

            if (hintsUsed > 0) {
                Text(
                    text = "使用提示: $hintsUsed 次",
                    style = MaterialTheme.typography.bodyLarge
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 评价
            Text(
                text = when {
                    totalScore >= 90 -> "太棒了！背诵得很流利！"
                    totalScore >= 70 -> "不错哦！继续努力！"
                    else -> "加油！多练习会更好！"
                },
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 背诵结果卡片
 */
@Composable
fun RecitationResultCard(
    result: RecitationResultItem
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (result.isCorrect) {
                MaterialTheme.colorScheme.surfaceVariant
            } else {
                MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 结果图标
            Text(
                text = if (result.isCorrect) "✅" else "❌",
                style = MaterialTheme.typography.headlineMedium
            )

            Spacer(modifier = Modifier.width(16.dp))

            // 句子信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "原句：${result.sentence}",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = "背诵：${result.userInput}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (result.isCorrect) {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    } else {
                        MaterialTheme.colorScheme.error
                    }
                )

                if (!result.isCorrect) {
                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = "相似度：${(result.similarity * 100).toInt()}%",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                if (result.hintsUsed > 0) {
                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = "使用提示：${result.hintsUsed}次",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            // 分数和用时
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "${result.score}分",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = if (result.isCorrect) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.error
                    }
                )

                Spacer(modifier = Modifier.height(4.dp))

                val seconds = result.timeSpent / 1000
                Text(
                    text = "${seconds}秒",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

// 数据类定义
data class RecitationResultItem(
    val sentence: String,
    val userInput: String,
    val similarity: Float,
    val isCorrect: Boolean,
    val score: Int,
    val timeSpent: Long,
    val hintsUsed: Int
)
