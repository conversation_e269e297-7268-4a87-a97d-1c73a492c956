package com.homework.assistant.ui.screens.photocheck

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.homework.assistant.ui.components.AppTopBar
import com.homework.assistant.ui.navigation.NavigationManager

@Composable
fun PhotoCheckMainScreen(navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "拍照检查", onNavigationClick = { navigationManager.navigateBack() })
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "📷",
                style = MaterialTheme.typography.displayLarge
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "拍照检查功能",
                style = MaterialTheme.typography.headlineMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "即将推出...",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun PhotoCheckCameraScreen(navigationManager: NavigationManager) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "拍照", onNavigationClick = { navigationManager.navigateBack() })
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("相机界面 - 即将推出")
        }
    }
}

@Composable
fun PhotoCheckResultScreen(
    imageUri: String,
    checkId: String,
    navigationManager: NavigationManager
) {
    Column(modifier = Modifier.fillMaxSize()) {
        AppTopBar(title = "检查结果", onNavigationClick = { navigationManager.navigateBack() })
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "📊",
                style = MaterialTheme.typography.displayLarge
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "检查结果",
                style = MaterialTheme.typography.headlineMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "图片: $imageUri",
                style = MaterialTheme.typography.bodyMedium
            )
            
            Text(
                text = "检查ID: $checkId",
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}
