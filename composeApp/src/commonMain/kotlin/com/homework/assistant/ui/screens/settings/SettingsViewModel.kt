package com.homework.assistant.ui.screens.settings

import androidx.lifecycle.viewModelScope
import com.homework.assistant.shared.data.model.LearningStatistics
import com.homework.assistant.shared.data.model.UserPreferences
import com.homework.assistant.shared.data.repository.StatisticsRepository
import com.homework.assistant.shared.data.repository.PreferencesRepository
import com.homework.assistant.ui.base.BaseViewModel
import com.homework.assistant.ui.base.UiState
import com.homework.assistant.ui.base.UiEvent
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 设置ViewModel
 */
class SettingsViewModel(
    private val statisticsRepository: StatisticsRepository,
    private val preferencesRepository: PreferencesRepository
) : BaseViewModel<SettingsUiState, SettingsUiEvent>() {
    
    override val _uiState = MutableStateFlow(
        SettingsUiState(
            statistics = null,
            preferences = UserPreferences(),
            isDarkTheme = false,
            isNotificationEnabled = true,
            voiceSpeed = 1.0f,
            autoBackup = true,
            cacheSize = "0 MB"
        )
    )
    
    init {
        loadData()
    }
    
    override fun handleEvent(event: SettingsUiEvent) {
        when (event) {
            is SettingsUiEvent.LoadStatistics -> loadStatistics()
            is SettingsUiEvent.LoadPreferences -> loadPreferences()
            is SettingsUiEvent.UpdateTheme -> updateTheme(event.isDark)
            is SettingsUiEvent.UpdateNotification -> updateNotification(event.enabled)
            is SettingsUiEvent.UpdateVoiceSpeed -> updateVoiceSpeed(event.speed)
            is SettingsUiEvent.UpdateAutoBackup -> updateAutoBackup(event.enabled)
            is SettingsUiEvent.ClearCache -> clearCache()
            is SettingsUiEvent.ExportData -> exportData()
            is SettingsUiEvent.ImportData -> importData(event.filePath)
            is SettingsUiEvent.ResetSettings -> resetSettings()
        }
    }
    
    private fun loadData() {
        loadStatistics()
        loadPreferences()
    }
    
    private fun loadStatistics() {
        viewModelScope.launch {
            statisticsRepository.getLearningStatistics().collectLatest { statistics ->
                updateState { it.copy(statistics = statistics) }
            }
        }
    }
    
    private fun loadPreferences() {
        viewModelScope.launch {
            preferencesRepository.getUserPreferences().collectLatest { preferences ->
                updateState { 
                    it.copy(
                        preferences = preferences,
                        isDarkTheme = preferences.isDarkTheme,
                        isNotificationEnabled = preferences.isNotificationEnabled,
                        voiceSpeed = preferences.voiceSpeed,
                        autoBackup = preferences.autoBackup
                    )
                }
            }
        }
    }
    
    private fun updateTheme(isDark: Boolean) {
        launchSafely {
            val updatedPreferences = _uiState.value.preferences.copy(isDarkTheme = isDark)
            preferencesRepository.updateUserPreferences(updatedPreferences)
            updateState { it.copy(isDarkTheme = isDark) }
        }
    }
    
    private fun updateNotification(enabled: Boolean) {
        launchSafely {
            val updatedPreferences = _uiState.value.preferences.copy(isNotificationEnabled = enabled)
            preferencesRepository.updateUserPreferences(updatedPreferences)
            updateState { it.copy(isNotificationEnabled = enabled) }
        }
    }
    
    private fun updateVoiceSpeed(speed: Float) {
        launchSafely {
            val updatedPreferences = _uiState.value.preferences.copy(voiceSpeed = speed)
            preferencesRepository.updateUserPreferences(updatedPreferences)
            updateState { it.copy(voiceSpeed = speed) }
        }
    }
    
    private fun updateAutoBackup(enabled: Boolean) {
        launchSafely {
            val updatedPreferences = _uiState.value.preferences.copy(autoBackup = enabled)
            preferencesRepository.updateUserPreferences(updatedPreferences)
            updateState { it.copy(autoBackup = enabled) }
        }
    }
    
    private fun clearCache() {
        launchSafely(showLoading = true) {
            // TODO: 实现清除缓存逻辑
            updateState { it.copy(cacheSize = "0 MB") }
        }
    }
    
    private fun exportData() {
        launchSafely(showLoading = true) {
            // TODO: 实现数据导出逻辑
        }
    }
    
    private fun importData(filePath: String) {
        launchSafely(showLoading = true) {
            // TODO: 实现数据导入逻辑
        }
    }
    
    private fun resetSettings() {
        launchSafely(showLoading = true) {
            val defaultPreferences = UserPreferences()
            preferencesRepository.updateUserPreferences(defaultPreferences)
            updateState { 
                it.copy(
                    preferences = defaultPreferences,
                    isDarkTheme = defaultPreferences.isDarkTheme,
                    isNotificationEnabled = defaultPreferences.isNotificationEnabled,
                    voiceSpeed = defaultPreferences.voiceSpeed,
                    autoBackup = defaultPreferences.autoBackup
                )
            }
        }
    }
}

/**
 * 设置UI状态
 */
data class SettingsUiState(
    val statistics: LearningStatistics?,
    val preferences: UserPreferences,
    val isDarkTheme: Boolean,
    val isNotificationEnabled: Boolean,
    val voiceSpeed: Float,
    val autoBackup: Boolean,
    val cacheSize: String
) : UiState

/**
 * 设置UI事件
 */
sealed class SettingsUiEvent : UiEvent {
    data object LoadStatistics : SettingsUiEvent()
    data object LoadPreferences : SettingsUiEvent()
    data class UpdateTheme(val isDark: Boolean) : SettingsUiEvent()
    data class UpdateNotification(val enabled: Boolean) : SettingsUiEvent()
    data class UpdateVoiceSpeed(val speed: Float) : SettingsUiEvent()
    data class UpdateAutoBackup(val enabled: Boolean) : SettingsUiEvent()
    data object ClearCache : SettingsUiEvent()
    data object ExportData : SettingsUiEvent()
    data class ImportData(val filePath: String) : SettingsUiEvent()
    data object ResetSettings : SettingsUiEvent()
}
