  App android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  App android.content.Context  
setContent android.content.Context  App android.content.ContextWrapper  
setContent android.content.ContextWrapper  Bundle 
android.os  	getString android.os.BaseBundle  	getString android.os.Bundle  App  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  App #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  App -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  AnimatedContentScope androidx.compose.animation  AboutScreen /androidx.compose.animation.AnimatedContentScope  DictationMainScreen /androidx.compose.animation.AnimatedContentScope  DictationResultScreen /androidx.compose.animation.AnimatedContentScope  DictationSessionScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  ManagementMainScreen /androidx.compose.animation.AnimatedContentScope  PhotoCheckCameraScreen /androidx.compose.animation.AnimatedContentScope  PhotoCheckMainScreen /androidx.compose.animation.AnimatedContentScope  PhotoCheckResultScreen /androidx.compose.animation.AnimatedContentScope  PreferencesScreen /androidx.compose.animation.AnimatedContentScope  RecitationAddScreen /androidx.compose.animation.AnimatedContentScope  RecitationEditScreen /androidx.compose.animation.AnimatedContentScope  RecitationListScreen /androidx.compose.animation.AnimatedContentScope  RecitationMainScreen /androidx.compose.animation.AnimatedContentScope  RecitationResultScreen /androidx.compose.animation.AnimatedContentScope  RecitationSessionScreen /androidx.compose.animation.AnimatedContentScope  SettingsMainScreen /androidx.compose.animation.AnimatedContentScope  StatisticsScreen /androidx.compose.animation.AnimatedContentScope  
WordAddScreen /androidx.compose.animation.AnimatedContentScope  WordCategoriesScreen /androidx.compose.animation.AnimatedContentScope  WordEditScreen /androidx.compose.animation.AnimatedContentScope  WordListScreen /androidx.compose.animation.AnimatedContentScope  	emptyList /androidx.compose.animation.AnimatedContentScope  split /androidx.compose.animation.AnimatedContentScope  BorderStroke androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  Add "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  	AppColors "androidx.compose.foundation.layout  	AppTopBar "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  	ArrowBack "androidx.compose.foundation.layout  ArrowForward "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Check "androidx.compose.foundation.layout  Checkbox "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  
ControlButton "androidx.compose.foundation.layout  CurrentWordDisplay "androidx.compose.foundation.layout  CustomTextStyles "androidx.compose.foundation.layout  DictationControlButtons "androidx.compose.foundation.layout  DictationProgressCard "androidx.compose.foundation.layout  DictationResultCard "androidx.compose.foundation.layout  DictationResultItem "androidx.compose.foundation.layout  DictationSettings "androidx.compose.foundation.layout  DictationSummaryCard "androidx.compose.foundation.layout  
EmptyState "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  FeatureCard "androidx.compose.foundation.layout  FeatureGrid "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  	GridCells "androidx.compose.foundation.layout  Home "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  ImageVector "androidx.compose.foundation.layout  	ImeAction "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  KeyboardActions "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LinearProgressIndicator "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Long "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  NavigationManager "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  Refresh "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Screen "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  Slider "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TopAppBarDefaults "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  UserInputSection "androidx.compose.foundation.layout  WelcomeSection "androidx.compose.foundation.layout  Word "androidx.compose.foundation.layout  WordSelectionCard "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  average "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  count "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  filter "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  kotlinx "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  map "androidx.compose.foundation.layout  minus "androidx.compose.foundation.layout  mutableFloatStateOf "androidx.compose.foundation.layout  mutableIntStateOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  outlinedButtonColors "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  plus "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  rangeTo "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  sessionRoute "androidx.compose.foundation.layout  setOf "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  sumOf "androidx.compose.foundation.layout  topAppBarColors "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  Icon +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  	AppColors .androidx.compose.foundation.layout.ColumnScope  	AppTopBar .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  ArrowForward .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Check .androidx.compose.foundation.layout.ColumnScope  Checkbox .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
ControlButton .androidx.compose.foundation.layout.ColumnScope  CurrentWordDisplay .androidx.compose.foundation.layout.ColumnScope  CustomTextStyles .androidx.compose.foundation.layout.ColumnScope  DictationControlButtons .androidx.compose.foundation.layout.ColumnScope  DictationProgressCard .androidx.compose.foundation.layout.ColumnScope  DictationResultCard .androidx.compose.foundation.layout.ColumnScope  DictationSummaryCard .androidx.compose.foundation.layout.ColumnScope  
EmptyState .androidx.compose.foundation.layout.ColumnScope  FeatureGrid .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Home .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  	ImeAction .androidx.compose.foundation.layout.ColumnScope  Info .androidx.compose.foundation.layout.ColumnScope  KeyboardActions .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  Refresh .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Screen .androidx.compose.foundation.layout.ColumnScope  Settings .androidx.compose.foundation.layout.ColumnScope  Slider .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  UserInputSection .androidx.compose.foundation.layout.ColumnScope  WelcomeSection .androidx.compose.foundation.layout.ColumnScope  WordSelectionCard .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  filter .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  kotlinx .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  map .androidx.compose.foundation.layout.ColumnScope  minus .androidx.compose.foundation.layout.ColumnScope  outlinedButtonColors .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  plus .androidx.compose.foundation.layout.ColumnScope  rangeTo .androidx.compose.foundation.layout.ColumnScope  sessionRoute .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  sumOf .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  	AppColors +androidx.compose.foundation.layout.RowScope  	ArrowBack +androidx.compose.foundation.layout.RowScope  ArrowForward +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Check +androidx.compose.foundation.layout.RowScope  Checkbox +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  
ControlButton +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Home +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  	ImeAction +androidx.compose.foundation.layout.RowScope  Info +androidx.compose.foundation.layout.RowScope  KeyboardActions +androidx.compose.foundation.layout.RowScope  KeyboardOptions +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  OutlinedTextField +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Screen +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  unit 6androidx.compose.foundation.layout.androidx.compose.ui  Dp ;androidx.compose.foundation.layout.androidx.compose.ui.unit  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Button .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  DictationResultCard .androidx.compose.foundation.lazy.LazyItemScope  DictationSummaryCard .androidx.compose.foundation.lazy.LazyItemScope  Home .androidx.compose.foundation.lazy.LazyItemScope  Icon .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  OutlinedButton .androidx.compose.foundation.lazy.LazyItemScope  Refresh .androidx.compose.foundation.lazy.LazyItemScope  Screen .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  WordSelectionCard .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  minus .androidx.compose.foundation.lazy.LazyItemScope  plus .androidx.compose.foundation.lazy.LazyItemScope  sumOf .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  Button .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  DictationResultCard .androidx.compose.foundation.lazy.LazyListScope  DictationSummaryCard .androidx.compose.foundation.lazy.LazyListScope  Home .androidx.compose.foundation.lazy.LazyListScope  Icon .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  OutlinedButton .androidx.compose.foundation.lazy.LazyListScope  Refresh .androidx.compose.foundation.lazy.LazyListScope  Screen .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  WordSelectionCard .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  minus .androidx.compose.foundation.lazy.LazyListScope  plus .androidx.compose.foundation.lazy.LazyListScope  sumOf .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Fixed /androidx.compose.foundation.lazy.grid.GridCells  FeatureCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  FeatureCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  CircleShape !androidx.compose.foundation.shape  CornerBasedShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardActionScope  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  
isNotBlank 4androidx.compose.foundation.text.KeyboardActionScope  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  ArrowForward ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  AlertDialog &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  	AppTopBar &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  ArrowForward &androidx.compose.material.icons.filled  Boolean &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  Checkbox &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  
ControlButton &androidx.compose.material.icons.filled  CurrentWordDisplay &androidx.compose.material.icons.filled  CustomTextStyles &androidx.compose.material.icons.filled  DictationControlButtons &androidx.compose.material.icons.filled  DictationProgressCard &androidx.compose.material.icons.filled  DictationResultCard &androidx.compose.material.icons.filled  DictationResultItem &androidx.compose.material.icons.filled  DictationSettings &androidx.compose.material.icons.filled  DictationSummaryCard &androidx.compose.material.icons.filled  
EmptyState &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  ImageVector &androidx.compose.material.icons.filled  	ImeAction &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  Int &androidx.compose.material.icons.filled  KeyboardActions &androidx.compose.material.icons.filled  KeyboardOptions &androidx.compose.material.icons.filled  
LazyColumn &androidx.compose.material.icons.filled  LinearProgressIndicator &androidx.compose.material.icons.filled  List &androidx.compose.material.icons.filled  Long &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  NavigationManager &androidx.compose.material.icons.filled  OutlinedButton &androidx.compose.material.icons.filled  OutlinedTextField &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Screen &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Slider &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Switch &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  	TextAlign &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  UserInputSection &androidx.compose.material.icons.filled  Word &androidx.compose.material.icons.filled  WordSelectionCard &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  average &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  buttonColors &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  	clickable &androidx.compose.material.icons.filled  clip &androidx.compose.material.icons.filled  count &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  filter &androidx.compose.material.icons.filled  format &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  
isNotBlank &androidx.compose.material.icons.filled  
isNotEmpty &androidx.compose.material.icons.filled  kotlinx &androidx.compose.material.icons.filled  launch &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  map &androidx.compose.material.icons.filled  minus &androidx.compose.material.icons.filled  mutableFloatStateOf &androidx.compose.material.icons.filled  mutableIntStateOf &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  outlinedButtonColors &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  plus &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  rangeTo &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  sessionRoute &androidx.compose.material.icons.filled  setOf &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  spacedBy &androidx.compose.material.icons.filled  sumOf &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  Add androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  	AppColors androidx.compose.material3  	AppTopBar androidx.compose.material3  Arrangement androidx.compose.material3  	ArrowBack androidx.compose.material3  ArrowForward androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  Check androidx.compose.material3  Checkbox androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  
ControlButton androidx.compose.material3  CurrentWordDisplay androidx.compose.material3  CustomTextStyles androidx.compose.material3  DictationControlButtons androidx.compose.material3  DictationProgressCard androidx.compose.material3  DictationResultCard androidx.compose.material3  DictationResultItem androidx.compose.material3  DictationSettings androidx.compose.material3  DictationSummaryCard androidx.compose.material3  
EmptyState androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FeatureCard androidx.compose.material3  FeatureGrid androidx.compose.material3  
FontWeight androidx.compose.material3  	GridCells androidx.compose.material3  Home androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  ImageVector androidx.compose.material3  	ImeAction androidx.compose.material3  Info androidx.compose.material3  Int androidx.compose.material3  KeyboardActions androidx.compose.material3  KeyboardOptions androidx.compose.material3  
LazyColumn androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  List androidx.compose.material3  Long androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  NavigationManager androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  	PlayArrow androidx.compose.material3  Refresh androidx.compose.material3  Row androidx.compose.material3  RowScope androidx.compose.material3  Screen androidx.compose.material3  Settings androidx.compose.material3  Shapes androidx.compose.material3  Slider androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Switch androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  UserInputSection androidx.compose.material3  WelcomeSection androidx.compose.material3  Word androidx.compose.material3  WordSelectionCard androidx.compose.material3  androidx androidx.compose.material3  average androidx.compose.material3  
background androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  	clickable androidx.compose.material3  clip androidx.compose.material3  count androidx.compose.material3  darkColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  filter androidx.compose.material3  format androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  kotlinx androidx.compose.material3  launch androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  map androidx.compose.material3  minus androidx.compose.material3  mutableFloatStateOf androidx.compose.material3  mutableIntStateOf androidx.compose.material3  mutableStateOf androidx.compose.material3  outlinedButtonColors androidx.compose.material3  padding androidx.compose.material3  plus androidx.compose.material3  provideDelegate androidx.compose.material3  rangeTo androidx.compose.material3  remember androidx.compose.material3  sessionRoute androidx.compose.material3  setOf androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  sumOf androidx.compose.material3  topAppBarColors androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  outlinedButtonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  outline &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  secondaryContainer &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  	AppColors (androidx.compose.material3.MaterialTheme  colorScheme (androidx.compose.material3.MaterialTheme  shapes (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  large !androidx.compose.material3.Shapes  medium !androidx.compose.material3.Shapes  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  displayLarge %androidx.compose.material3.Typography  
displayMedium %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  unit .androidx.compose.material3.androidx.compose.ui  Dp 3androidx.compose.material3.androidx.compose.ui.unit  Add androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  	AppColors androidx.compose.runtime  	AppTopBar androidx.compose.runtime  Arrangement androidx.compose.runtime  	ArrowBack androidx.compose.runtime  ArrowForward androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  Check androidx.compose.runtime  Checkbox androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  
ControlButton androidx.compose.runtime  CurrentWordDisplay androidx.compose.runtime  CustomTextStyles androidx.compose.runtime  DictationControlButtons androidx.compose.runtime  DictationProgressCard androidx.compose.runtime  DictationResultCard androidx.compose.runtime  DictationResultItem androidx.compose.runtime  DictationSettings androidx.compose.runtime  DictationSummaryCard androidx.compose.runtime  
EmptyState androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  FeatureCard androidx.compose.runtime  FeatureGrid androidx.compose.runtime  
FontWeight androidx.compose.runtime  	GridCells androidx.compose.runtime  Home androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  ImageVector androidx.compose.runtime  	ImeAction androidx.compose.runtime  Info androidx.compose.runtime  Int androidx.compose.runtime  KeyboardActions androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LinearProgressIndicator androidx.compose.runtime  List androidx.compose.runtime  Long androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableFloatState androidx.compose.runtime  MutableIntState androidx.compose.runtime  MutableState androidx.compose.runtime  NavigationManager androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  	PlayArrow androidx.compose.runtime  Preview androidx.compose.runtime  Refresh androidx.compose.runtime  Row androidx.compose.runtime  RowScope androidx.compose.runtime  Screen androidx.compose.runtime  Settings androidx.compose.runtime  Slider androidx.compose.runtime  Spacer androidx.compose.runtime  String androidx.compose.runtime  Switch androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TopAppBarDefaults androidx.compose.runtime  Unit androidx.compose.runtime  UserInputSection androidx.compose.runtime  WelcomeSection androidx.compose.runtime  Word androidx.compose.runtime  WordSelectionCard androidx.compose.runtime  androidx androidx.compose.runtime  average androidx.compose.runtime  
background androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  
cardElevation androidx.compose.runtime  	clickable androidx.compose.runtime  clip androidx.compose.runtime  count androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  filter androidx.compose.runtime  format androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  kotlinx androidx.compose.runtime  launch androidx.compose.runtime  listOf androidx.compose.runtime  map androidx.compose.runtime  minus androidx.compose.runtime  mutableFloatStateOf androidx.compose.runtime  mutableIntStateOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  outlinedButtonColors androidx.compose.runtime  padding androidx.compose.runtime  plus androidx.compose.runtime  provideDelegate androidx.compose.runtime  rangeTo androidx.compose.runtime  remember androidx.compose.runtime  sessionRoute androidx.compose.runtime  setOf androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  sumOf androidx.compose.runtime  topAppBarColors androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  setValue *androidx.compose.runtime.MutableFloatState  setValue (androidx.compose.runtime.MutableIntState  setValue %androidx.compose.runtime.MutableState  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  unit ,androidx.compose.runtime.androidx.compose.ui  Dp 1androidx.compose.runtime.androidx.compose.ui.unit  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  End androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  End 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  	clickable &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  White ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  	Companion (androidx.compose.ui.text.input.ImeAction  Done (androidx.compose.ui.text.input.ImeAction  Done 2androidx.compose.ui.text.input.ImeAction.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  App #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  NavBackStackEntry androidx.navigation  NavGraph androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  	arguments %androidx.navigation.NavBackStackEntry  graph !androidx.navigation.NavController  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  startDestinationRoute androidx.navigation.NavGraph  AboutScreen #androidx.navigation.NavGraphBuilder  DictationMainScreen #androidx.navigation.NavGraphBuilder  DictationResultScreen #androidx.navigation.NavGraphBuilder  DictationSessionScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  ManagementMainScreen #androidx.navigation.NavGraphBuilder  PhotoCheckCameraScreen #androidx.navigation.NavGraphBuilder  PhotoCheckMainScreen #androidx.navigation.NavGraphBuilder  PhotoCheckResultScreen #androidx.navigation.NavGraphBuilder  PreferencesScreen #androidx.navigation.NavGraphBuilder  RecitationAddScreen #androidx.navigation.NavGraphBuilder  RecitationEditScreen #androidx.navigation.NavGraphBuilder  RecitationListScreen #androidx.navigation.NavGraphBuilder  RecitationMainScreen #androidx.navigation.NavGraphBuilder  RecitationResultScreen #androidx.navigation.NavGraphBuilder  RecitationSessionScreen #androidx.navigation.NavGraphBuilder  Screen #androidx.navigation.NavGraphBuilder  SettingsMainScreen #androidx.navigation.NavGraphBuilder  StatisticsScreen #androidx.navigation.NavGraphBuilder  
WordAddScreen #androidx.navigation.NavGraphBuilder  WordCategoriesScreen #androidx.navigation.NavGraphBuilder  WordEditScreen #androidx.navigation.NavGraphBuilder  WordListScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  	emptyList #androidx.navigation.NavGraphBuilder  split #androidx.navigation.NavGraphBuilder  graph %androidx.navigation.NavHostController  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  Screen %androidx.navigation.NavOptionsBuilder  
navController %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  	inclusive "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  App com.homework.assistant  AppAndroidPreview com.homework.assistant  Bundle com.homework.assistant  ComponentActivity com.homework.assistant  
Composable com.homework.assistant  MainActivity com.homework.assistant  Preview com.homework.assistant  App #com.homework.assistant.MainActivity  
setContent #com.homework.assistant.MainActivity  StudySession (com.homework.assistant.shared.data.model  Word (com.homework.assistant.shared.data.model  category -com.homework.assistant.shared.data.model.Word  	character -com.homework.assistant.shared.data.model.Word  id -com.homework.assistant.shared.data.model.Word  meaning -com.homework.assistant.shared.data.model.Word  pinyin -com.homework.assistant.shared.data.model.Word  DictationProgress 'com.homework.assistant.shared.dictation  DictationResult 'com.homework.assistant.shared.dictation  DictationService 'com.homework.assistant.shared.dictation  DictationSession 'com.homework.assistant.shared.dictation  DictationSessionState 'com.homework.assistant.shared.dictation  DictationSettings 'com.homework.assistant.shared.dictation  DictationState 'com.homework.assistant.shared.dictation  finishDictation 8com.homework.assistant.shared.dictation.DictationService  getDictationState 8com.homework.assistant.shared.dictation.DictationService  handleVoiceCommand 8com.homework.assistant.shared.dictation.DictationService  nextWord 8com.homework.assistant.shared.dictation.DictationService  pauseDictation 8com.homework.assistant.shared.dictation.DictationService  previousWord 8com.homework.assistant.shared.dictation.DictationService  repeatCurrentWord 8com.homework.assistant.shared.dictation.DictationService  resumeDictation 8com.homework.assistant.shared.dictation.DictationService  showWordMeaning 8com.homework.assistant.shared.dictation.DictationService  speakCurrentWord 8com.homework.assistant.shared.dictation.DictationService  speakWordPinyin 8com.homework.assistant.shared.dictation.DictationService  startDictation 8com.homework.assistant.shared.dictation.DictationService  submitAnswer 8com.homework.assistant.shared.dictation.DictationService  state 8com.homework.assistant.shared.dictation.DictationSession  	CANCELLED =com.homework.assistant.shared.dictation.DictationSessionState  	COMPLETED =com.homework.assistant.shared.dictation.DictationSessionState  	Companion =com.homework.assistant.shared.dictation.DictationSessionState  availableCommands 6com.homework.assistant.shared.dictation.DictationState  currentWord 6com.homework.assistant.shared.dictation.DictationState  isListening 6com.homework.assistant.shared.dictation.DictationState  	isPlaying 6com.homework.assistant.shared.dictation.DictationState  
lastResult 6com.homework.assistant.shared.dictation.DictationState  progress 6com.homework.assistant.shared.dictation.DictationState  session 6com.homework.assistant.shared.dictation.DictationState  Any com.homework.assistant.ui.base  
BaseViewModel com.homework.assistant.ui.base  Boolean com.homework.assistant.ui.base  CoroutineExceptionHandler com.homework.assistant.ui.base  Effect com.homework.assistant.ui.base  Int com.homework.assistant.ui.base  List com.homework.assistant.ui.base  LoadingState com.homework.assistant.ui.base  Map com.homework.assistant.ui.base  MutableStateFlow com.homework.assistant.ui.base  	StateFlow com.homework.assistant.ui.base  String com.homework.assistant.ui.base  T com.homework.assistant.ui.base  	Throwable com.homework.assistant.ui.base  UiEffect com.homework.assistant.ui.base  UiEvent com.homework.assistant.ui.base  UiState com.homework.assistant.ui.base  Unit com.homework.assistant.ui.base  	ViewModel com.homework.assistant.ui.base  
_isLoading com.homework.assistant.ui.base  asStateFlow com.homework.assistant.ui.base  
clearError com.homework.assistant.ui.base  	emptyList com.homework.assistant.ui.base  launch com.homework.assistant.ui.base  CoroutineExceptionHandler ,com.homework.assistant.ui.base.BaseViewModel  MutableStateFlow ,com.homework.assistant.ui.base.BaseViewModel  _error ,com.homework.assistant.ui.base.BaseViewModel  
_isLoading ,com.homework.assistant.ui.base.BaseViewModel  _uiState ,com.homework.assistant.ui.base.BaseViewModel  asStateFlow ,com.homework.assistant.ui.base.BaseViewModel  
clearError ,com.homework.assistant.ui.base.BaseViewModel  exceptionHandler ,com.homework.assistant.ui.base.BaseViewModel  handleError ,com.homework.assistant.ui.base.BaseViewModel  launch ,com.homework.assistant.ui.base.BaseViewModel  viewModelScope ,com.homework.assistant.ui.base.BaseViewModel  MutableStateFlow 7com.homework.assistant.ui.base.BaseViewModelWithEffects  _effect 7com.homework.assistant.ui.base.BaseViewModelWithEffects  asStateFlow 7com.homework.assistant.ui.base.BaseViewModelWithEffects  LoadingState +com.homework.assistant.ui.base.LoadingState  String +com.homework.assistant.ui.base.LoadingState  T +com.homework.assistant.ui.base.LoadingState  String 'com.homework.assistant.ui.base.UiEffect  UiEffect 'com.homework.assistant.ui.base.UiEffect  	Alignment $com.homework.assistant.ui.components  	AppColors $com.homework.assistant.ui.components  	AppTopBar $com.homework.assistant.ui.components  Boolean $com.homework.assistant.ui.components  Box $com.homework.assistant.ui.components  Button $com.homework.assistant.ui.components  ButtonDefaults $com.homework.assistant.ui.components  Card $com.homework.assistant.ui.components  CardDefaults $com.homework.assistant.ui.components  CircularProgressIndicator $com.homework.assistant.ui.components  Color $com.homework.assistant.ui.components  Column $com.homework.assistant.ui.components  
Composable $com.homework.assistant.ui.components  
EmptyState $com.homework.assistant.ui.components  ExperimentalMaterial3Api $com.homework.assistant.ui.components  FeatureCard $com.homework.assistant.ui.components  
FontWeight $com.homework.assistant.ui.components  Icon $com.homework.assistant.ui.components  
IconButton $com.homework.assistant.ui.components  Icons $com.homework.assistant.ui.components  ImageVector $com.homework.assistant.ui.components  
MaterialTheme $com.homework.assistant.ui.components  Modifier $com.homework.assistant.ui.components  OptIn $com.homework.assistant.ui.components  Row $com.homework.assistant.ui.components  RowScope $com.homework.assistant.ui.components  Spacer $com.homework.assistant.ui.components  String $com.homework.assistant.ui.components  Text $com.homework.assistant.ui.components  	TextAlign $com.homework.assistant.ui.components  	TopAppBar $com.homework.assistant.ui.components  TopAppBarDefaults $com.homework.assistant.ui.components  Unit $com.homework.assistant.ui.components  androidx $com.homework.assistant.ui.components  buttonColors $com.homework.assistant.ui.components  
cardColors $com.homework.assistant.ui.components  
cardElevation $com.homework.assistant.ui.components  fillMaxWidth $com.homework.assistant.ui.components  height $com.homework.assistant.ui.components  padding $com.homework.assistant.ui.components  size $com.homework.assistant.ui.components  topAppBarColors $com.homework.assistant.ui.components  weight $com.homework.assistant.ui.components  width $com.homework.assistant.ui.components  compose -com.homework.assistant.ui.components.androidx  ui 5com.homework.assistant.ui.components.androidx.compose  unit 8com.homework.assistant.ui.components.androidx.compose.ui  Dp =com.homework.assistant.ui.components.androidx.compose.ui.unit  AboutScreen $com.homework.assistant.ui.navigation  
AppNavigation $com.homework.assistant.ui.navigation  Boolean $com.homework.assistant.ui.navigation  
Composable $com.homework.assistant.ui.navigation  DictationMainScreen $com.homework.assistant.ui.navigation  DictationResultScreen $com.homework.assistant.ui.navigation  DictationSessionScreen $com.homework.assistant.ui.navigation  
HomeScreen $com.homework.assistant.ui.navigation  List $com.homework.assistant.ui.navigation  ManagementMainScreen $com.homework.assistant.ui.navigation  NavHostController $com.homework.assistant.ui.navigation  NavigationManager $com.homework.assistant.ui.navigation  PhotoCheckCameraScreen $com.homework.assistant.ui.navigation  PhotoCheckMainScreen $com.homework.assistant.ui.navigation  PhotoCheckResultScreen $com.homework.assistant.ui.navigation  PreferencesScreen $com.homework.assistant.ui.navigation  RecitationAddScreen $com.homework.assistant.ui.navigation  RecitationEditScreen $com.homework.assistant.ui.navigation  RecitationListScreen $com.homework.assistant.ui.navigation  RecitationMainScreen $com.homework.assistant.ui.navigation  RecitationResultScreen $com.homework.assistant.ui.navigation  RecitationSessionScreen $com.homework.assistant.ui.navigation  Screen $com.homework.assistant.ui.navigation  SettingsMainScreen $com.homework.assistant.ui.navigation  StatisticsScreen $com.homework.assistant.ui.navigation  String $com.homework.assistant.ui.navigation  
WordAddScreen $com.homework.assistant.ui.navigation  WordCategoriesScreen $com.homework.assistant.ui.navigation  WordEditScreen $com.homework.assistant.ui.navigation  WordListScreen $com.homework.assistant.ui.navigation  	emptyList $com.homework.assistant.ui.navigation  joinToString $com.homework.assistant.ui.navigation  
navController $com.homework.assistant.ui.navigation  split $com.homework.assistant.ui.navigation  Screen 6com.homework.assistant.ui.navigation.NavigationManager  
navController 6com.homework.assistant.ui.navigation.NavigationManager  navigateBack 6com.homework.assistant.ui.navigation.NavigationManager  navigateBackTo 6com.homework.assistant.ui.navigation.NavigationManager  
navigateTo 6com.homework.assistant.ui.navigation.NavigationManager  	Dictation +com.homework.assistant.ui.navigation.Screen  HOME +com.homework.assistant.ui.navigation.Screen  List +com.homework.assistant.ui.navigation.Screen  
Management +com.homework.assistant.ui.navigation.Screen  
PhotoCheck +com.homework.assistant.ui.navigation.Screen  
Recitation +com.homework.assistant.ui.navigation.Screen  Settings +com.homework.assistant.ui.navigation.Screen  String +com.homework.assistant.ui.navigation.Screen  joinToString +com.homework.assistant.ui.navigation.Screen  MAIN 5com.homework.assistant.ui.navigation.Screen.Dictation  RESULT 5com.homework.assistant.ui.navigation.Screen.Dictation  SESSION 5com.homework.assistant.ui.navigation.Screen.Dictation  joinToString 5com.homework.assistant.ui.navigation.Screen.Dictation  sessionRoute 5com.homework.assistant.ui.navigation.Screen.Dictation  MAIN 6com.homework.assistant.ui.navigation.Screen.Management  Recitations 6com.homework.assistant.ui.navigation.Screen.Management  String 6com.homework.assistant.ui.navigation.Screen.Management  Words 6com.homework.assistant.ui.navigation.Screen.Management  ADD Bcom.homework.assistant.ui.navigation.Screen.Management.Recitations  EDIT Bcom.homework.assistant.ui.navigation.Screen.Management.Recitations  LIST Bcom.homework.assistant.ui.navigation.Screen.Management.Recitations  ADD <com.homework.assistant.ui.navigation.Screen.Management.Words  
CATEGORIES <com.homework.assistant.ui.navigation.Screen.Management.Words  EDIT <com.homework.assistant.ui.navigation.Screen.Management.Words  LIST <com.homework.assistant.ui.navigation.Screen.Management.Words  CAMERA 6com.homework.assistant.ui.navigation.Screen.PhotoCheck  MAIN 6com.homework.assistant.ui.navigation.Screen.PhotoCheck  RESULT 6com.homework.assistant.ui.navigation.Screen.PhotoCheck  MAIN 6com.homework.assistant.ui.navigation.Screen.Recitation  RESULT 6com.homework.assistant.ui.navigation.Screen.Recitation  SESSION 6com.homework.assistant.ui.navigation.Screen.Recitation  ABOUT 4com.homework.assistant.ui.navigation.Screen.Settings  MAIN 4com.homework.assistant.ui.navigation.Screen.Settings  PREFERENCES 4com.homework.assistant.ui.navigation.Screen.Settings  
STATISTICS 4com.homework.assistant.ui.navigation.Screen.Settings  Add +com.homework.assistant.ui.screens.dictation  AlertDialog +com.homework.assistant.ui.screens.dictation  	Alignment +com.homework.assistant.ui.screens.dictation  	AppTopBar +com.homework.assistant.ui.screens.dictation  Arrangement +com.homework.assistant.ui.screens.dictation  	ArrowBack +com.homework.assistant.ui.screens.dictation  ArrowForward +com.homework.assistant.ui.screens.dictation  
BaseViewModel +com.homework.assistant.ui.screens.dictation  Boolean +com.homework.assistant.ui.screens.dictation  Box +com.homework.assistant.ui.screens.dictation  Button +com.homework.assistant.ui.screens.dictation  ButtonDefaults +com.homework.assistant.ui.screens.dictation  Card +com.homework.assistant.ui.screens.dictation  CardDefaults +com.homework.assistant.ui.screens.dictation  Check +com.homework.assistant.ui.screens.dictation  Checkbox +com.homework.assistant.ui.screens.dictation  CircleShape +com.homework.assistant.ui.screens.dictation  CircularProgressIndicator +com.homework.assistant.ui.screens.dictation  Column +com.homework.assistant.ui.screens.dictation  
Composable +com.homework.assistant.ui.screens.dictation  
ControlButton +com.homework.assistant.ui.screens.dictation  CurrentWordDisplay +com.homework.assistant.ui.screens.dictation  CustomTextStyles +com.homework.assistant.ui.screens.dictation  DictationControlButtons +com.homework.assistant.ui.screens.dictation  DictationMainScreen +com.homework.assistant.ui.screens.dictation  DictationProgress +com.homework.assistant.ui.screens.dictation  DictationProgressCard +com.homework.assistant.ui.screens.dictation  DictationResult +com.homework.assistant.ui.screens.dictation  DictationResultCard +com.homework.assistant.ui.screens.dictation  DictationResultItem +com.homework.assistant.ui.screens.dictation  DictationResultScreen +com.homework.assistant.ui.screens.dictation  DictationService +com.homework.assistant.ui.screens.dictation  DictationSession +com.homework.assistant.ui.screens.dictation  DictationSessionScreen +com.homework.assistant.ui.screens.dictation  DictationSessionState +com.homework.assistant.ui.screens.dictation  DictationSettings +com.homework.assistant.ui.screens.dictation  DictationSettingsDialog +com.homework.assistant.ui.screens.dictation  DictationSummaryCard +com.homework.assistant.ui.screens.dictation  DictationUiEvent +com.homework.assistant.ui.screens.dictation  DictationUiState +com.homework.assistant.ui.screens.dictation  
EmptyState +com.homework.assistant.ui.screens.dictation  
FontWeight +com.homework.assistant.ui.screens.dictation  Home +com.homework.assistant.ui.screens.dictation  Icon +com.homework.assistant.ui.screens.dictation  
IconButton +com.homework.assistant.ui.screens.dictation  Icons +com.homework.assistant.ui.screens.dictation  ImageVector +com.homework.assistant.ui.screens.dictation  	ImeAction +com.homework.assistant.ui.screens.dictation  Info +com.homework.assistant.ui.screens.dictation  Int +com.homework.assistant.ui.screens.dictation  KeyboardActions +com.homework.assistant.ui.screens.dictation  KeyboardOptions +com.homework.assistant.ui.screens.dictation  
LazyColumn +com.homework.assistant.ui.screens.dictation  LinearProgressIndicator +com.homework.assistant.ui.screens.dictation  List +com.homework.assistant.ui.screens.dictation  Long +com.homework.assistant.ui.screens.dictation  
MaterialTheme +com.homework.assistant.ui.screens.dictation  Modifier +com.homework.assistant.ui.screens.dictation  MutableStateFlow +com.homework.assistant.ui.screens.dictation  NavigationManager +com.homework.assistant.ui.screens.dictation  OutlinedButton +com.homework.assistant.ui.screens.dictation  OutlinedTextField +com.homework.assistant.ui.screens.dictation  	PlayArrow +com.homework.assistant.ui.screens.dictation  Refresh +com.homework.assistant.ui.screens.dictation  Row +com.homework.assistant.ui.screens.dictation  Screen +com.homework.assistant.ui.screens.dictation  Settings +com.homework.assistant.ui.screens.dictation  Slider +com.homework.assistant.ui.screens.dictation  Spacer +com.homework.assistant.ui.screens.dictation  String +com.homework.assistant.ui.screens.dictation  Switch +com.homework.assistant.ui.screens.dictation  Text +com.homework.assistant.ui.screens.dictation  	TextAlign +com.homework.assistant.ui.screens.dictation  
TextButton +com.homework.assistant.ui.screens.dictation  UiEvent +com.homework.assistant.ui.screens.dictation  UiState +com.homework.assistant.ui.screens.dictation  Unit +com.homework.assistant.ui.screens.dictation  UserInputSection +com.homework.assistant.ui.screens.dictation  Word +com.homework.assistant.ui.screens.dictation  WordSelectionCard +com.homework.assistant.ui.screens.dictation  androidx +com.homework.assistant.ui.screens.dictation  average +com.homework.assistant.ui.screens.dictation  
background +com.homework.assistant.ui.screens.dictation  buttonColors +com.homework.assistant.ui.screens.dictation  
cardColors +com.homework.assistant.ui.screens.dictation  	clickable +com.homework.assistant.ui.screens.dictation  clip +com.homework.assistant.ui.screens.dictation  
collectLatest +com.homework.assistant.ui.screens.dictation  count +com.homework.assistant.ui.screens.dictation  dictationService +com.homework.assistant.ui.screens.dictation  	emptyList +com.homework.assistant.ui.screens.dictation  fillMaxSize +com.homework.assistant.ui.screens.dictation  fillMaxWidth +com.homework.assistant.ui.screens.dictation  filter +com.homework.assistant.ui.screens.dictation  format +com.homework.assistant.ui.screens.dictation  getValue +com.homework.assistant.ui.screens.dictation  height +com.homework.assistant.ui.screens.dictation  isBlank +com.homework.assistant.ui.screens.dictation  
isNotBlank +com.homework.assistant.ui.screens.dictation  
isNotEmpty +com.homework.assistant.ui.screens.dictation  kotlinx +com.homework.assistant.ui.screens.dictation  launch +com.homework.assistant.ui.screens.dictation  listOf +com.homework.assistant.ui.screens.dictation  map +com.homework.assistant.ui.screens.dictation  minus +com.homework.assistant.ui.screens.dictation  mutableFloatStateOf +com.homework.assistant.ui.screens.dictation  mutableIntStateOf +com.homework.assistant.ui.screens.dictation  mutableStateOf +com.homework.assistant.ui.screens.dictation  outlinedButtonColors +com.homework.assistant.ui.screens.dictation  padding +com.homework.assistant.ui.screens.dictation  plus +com.homework.assistant.ui.screens.dictation  provideDelegate +com.homework.assistant.ui.screens.dictation  rangeTo +com.homework.assistant.ui.screens.dictation  remember +com.homework.assistant.ui.screens.dictation  sessionRoute +com.homework.assistant.ui.screens.dictation  setOf +com.homework.assistant.ui.screens.dictation  setValue +com.homework.assistant.ui.screens.dictation  size +com.homework.assistant.ui.screens.dictation  spacedBy +com.homework.assistant.ui.screens.dictation  sumOf +com.homework.assistant.ui.screens.dictation  updateState +com.homework.assistant.ui.screens.dictation  weight +com.homework.assistant.ui.screens.dictation  width +com.homework.assistant.ui.screens.dictation  	isCorrect ?com.homework.assistant.ui.screens.dictation.DictationResultItem  score ?com.homework.assistant.ui.screens.dictation.DictationResultItem  	timeSpent ?com.homework.assistant.ui.screens.dictation.DictationResultItem  
userAnswer ?com.homework.assistant.ui.screens.dictation.DictationResultItem  word ?com.homework.assistant.ui.screens.dictation.DictationResultItem  DictationSettings <com.homework.assistant.ui.screens.dictation.DictationUiEvent  DictationUiEvent <com.homework.assistant.ui.screens.dictation.DictationUiEvent  FinishDictation <com.homework.assistant.ui.screens.dictation.DictationUiEvent  HandleVoiceCommand <com.homework.assistant.ui.screens.dictation.DictationUiEvent  HideMeaning <com.homework.assistant.ui.screens.dictation.DictationUiEvent  List <com.homework.assistant.ui.screens.dictation.DictationUiEvent  NextWord <com.homework.assistant.ui.screens.dictation.DictationUiEvent  PauseDictation <com.homework.assistant.ui.screens.dictation.DictationUiEvent  PreviousWord <com.homework.assistant.ui.screens.dictation.DictationUiEvent  
RepeatWord <com.homework.assistant.ui.screens.dictation.DictationUiEvent  ResumeDictation <com.homework.assistant.ui.screens.dictation.DictationUiEvent  ShowMeaning <com.homework.assistant.ui.screens.dictation.DictationUiEvent  SpeakCurrentWord <com.homework.assistant.ui.screens.dictation.DictationUiEvent  SpeakPinyin <com.homework.assistant.ui.screens.dictation.DictationUiEvent  StartDictation <com.homework.assistant.ui.screens.dictation.DictationUiEvent  String <com.homework.assistant.ui.screens.dictation.DictationUiEvent  SubmitAnswer <com.homework.assistant.ui.screens.dictation.DictationUiEvent  UpdateUserInput <com.homework.assistant.ui.screens.dictation.DictationUiEvent  Word <com.homework.assistant.ui.screens.dictation.DictationUiEvent  command Ocom.homework.assistant.ui.screens.dictation.DictationUiEvent.HandleVoiceCommand  settings Kcom.homework.assistant.ui.screens.dictation.DictationUiEvent.StartDictation  words Kcom.homework.assistant.ui.screens.dictation.DictationUiEvent.StartDictation  input Lcom.homework.assistant.ui.screens.dictation.DictationUiEvent.UpdateUserInput  DictationSessionState <com.homework.assistant.ui.screens.dictation.DictationUiState  copy <com.homework.assistant.ui.screens.dictation.DictationUiState  isListening <com.homework.assistant.ui.screens.dictation.DictationUiState  
isNotBlank <com.homework.assistant.ui.screens.dictation.DictationUiState  	isPlaying <com.homework.assistant.ui.screens.dictation.DictationUiState  session <com.homework.assistant.ui.screens.dictation.DictationUiState  	userInput <com.homework.assistant.ui.screens.dictation.DictationUiState  DictationProgress >com.homework.assistant.ui.screens.dictation.DictationViewModel  DictationUiState >com.homework.assistant.ui.screens.dictation.DictationViewModel  MutableStateFlow >com.homework.assistant.ui.screens.dictation.DictationViewModel  _uiState >com.homework.assistant.ui.screens.dictation.DictationViewModel  
collectLatest >com.homework.assistant.ui.screens.dictation.DictationViewModel  dictationService >com.homework.assistant.ui.screens.dictation.DictationViewModel  	emptyList >com.homework.assistant.ui.screens.dictation.DictationViewModel  finishDictation >com.homework.assistant.ui.screens.dictation.DictationViewModel  handleVoiceCommand >com.homework.assistant.ui.screens.dictation.DictationViewModel  hideMeaning >com.homework.assistant.ui.screens.dictation.DictationViewModel  isBlank >com.homework.assistant.ui.screens.dictation.DictationViewModel  launch >com.homework.assistant.ui.screens.dictation.DictationViewModel  launchSafely >com.homework.assistant.ui.screens.dictation.DictationViewModel  nextWord >com.homework.assistant.ui.screens.dictation.DictationViewModel  pauseDictation >com.homework.assistant.ui.screens.dictation.DictationViewModel  previousWord >com.homework.assistant.ui.screens.dictation.DictationViewModel  
repeatWord >com.homework.assistant.ui.screens.dictation.DictationViewModel  resumeDictation >com.homework.assistant.ui.screens.dictation.DictationViewModel  showMeaning >com.homework.assistant.ui.screens.dictation.DictationViewModel  speakCurrentWord >com.homework.assistant.ui.screens.dictation.DictationViewModel  speakPinyin >com.homework.assistant.ui.screens.dictation.DictationViewModel  startDictation >com.homework.assistant.ui.screens.dictation.DictationViewModel  submitAnswer >com.homework.assistant.ui.screens.dictation.DictationViewModel  updateState >com.homework.assistant.ui.screens.dictation.DictationViewModel  updateUserInput >com.homework.assistant.ui.screens.dictation.DictationViewModel  viewModelScope >com.homework.assistant.ui.screens.dictation.DictationViewModel  	Alignment &com.homework.assistant.ui.screens.home  	AppTopBar &com.homework.assistant.ui.screens.home  Arrangement &com.homework.assistant.ui.screens.home  Card &com.homework.assistant.ui.screens.home  CardDefaults &com.homework.assistant.ui.screens.home  Column &com.homework.assistant.ui.screens.home  
Composable &com.homework.assistant.ui.screens.home  FeatureCard &com.homework.assistant.ui.screens.home  FeatureGrid &com.homework.assistant.ui.screens.home  FeatureItem &com.homework.assistant.ui.screens.home  
FontWeight &com.homework.assistant.ui.screens.home  	GridCells &com.homework.assistant.ui.screens.home  
HomeScreen &com.homework.assistant.ui.screens.home  Icon &com.homework.assistant.ui.screens.home  
IconButton &com.homework.assistant.ui.screens.home  Icons &com.homework.assistant.ui.screens.home  
MaterialTheme &com.homework.assistant.ui.screens.home  Modifier &com.homework.assistant.ui.screens.home  NavigationManager &com.homework.assistant.ui.screens.home  Screen &com.homework.assistant.ui.screens.home  Spacer &com.homework.assistant.ui.screens.home  String &com.homework.assistant.ui.screens.home  Text &com.homework.assistant.ui.screens.home  	TextAlign &com.homework.assistant.ui.screens.home  WelcomeSection &com.homework.assistant.ui.screens.home  
cardColors &com.homework.assistant.ui.screens.home  fillMaxSize &com.homework.assistant.ui.screens.home  fillMaxWidth &com.homework.assistant.ui.screens.home  height &com.homework.assistant.ui.screens.home  listOf &com.homework.assistant.ui.screens.home  padding &com.homework.assistant.ui.screens.home  remember &com.homework.assistant.ui.screens.home  spacedBy &com.homework.assistant.ui.screens.home  description 2com.homework.assistant.ui.screens.home.FeatureItem  icon 2com.homework.assistant.ui.screens.home.FeatureItem  route 2com.homework.assistant.ui.screens.home.FeatureItem  title 2com.homework.assistant.ui.screens.home.FeatureItem  	Alignment ,com.homework.assistant.ui.screens.management  	AppTopBar ,com.homework.assistant.ui.screens.management  Arrangement ,com.homework.assistant.ui.screens.management  Column ,com.homework.assistant.ui.screens.management  
Composable ,com.homework.assistant.ui.screens.management  ManagementMainScreen ,com.homework.assistant.ui.screens.management  
MaterialTheme ,com.homework.assistant.ui.screens.management  Modifier ,com.homework.assistant.ui.screens.management  NavigationManager ,com.homework.assistant.ui.screens.management  Spacer ,com.homework.assistant.ui.screens.management  Text ,com.homework.assistant.ui.screens.management  fillMaxSize ,com.homework.assistant.ui.screens.management  height ,com.homework.assistant.ui.screens.management  padding ,com.homework.assistant.ui.screens.management  	Alignment 8com.homework.assistant.ui.screens.management.recitations  	AppTopBar 8com.homework.assistant.ui.screens.management.recitations  Box 8com.homework.assistant.ui.screens.management.recitations  Column 8com.homework.assistant.ui.screens.management.recitations  
Composable 8com.homework.assistant.ui.screens.management.recitations  Modifier 8com.homework.assistant.ui.screens.management.recitations  NavigationManager 8com.homework.assistant.ui.screens.management.recitations  RecitationAddScreen 8com.homework.assistant.ui.screens.management.recitations  RecitationEditScreen 8com.homework.assistant.ui.screens.management.recitations  RecitationListScreen 8com.homework.assistant.ui.screens.management.recitations  String 8com.homework.assistant.ui.screens.management.recitations  Text 8com.homework.assistant.ui.screens.management.recitations  fillMaxSize 8com.homework.assistant.ui.screens.management.recitations  	Alignment 2com.homework.assistant.ui.screens.management.words  	AppTopBar 2com.homework.assistant.ui.screens.management.words  Box 2com.homework.assistant.ui.screens.management.words  Column 2com.homework.assistant.ui.screens.management.words  
Composable 2com.homework.assistant.ui.screens.management.words  Modifier 2com.homework.assistant.ui.screens.management.words  NavigationManager 2com.homework.assistant.ui.screens.management.words  String 2com.homework.assistant.ui.screens.management.words  Text 2com.homework.assistant.ui.screens.management.words  
WordAddScreen 2com.homework.assistant.ui.screens.management.words  WordCategoriesScreen 2com.homework.assistant.ui.screens.management.words  WordEditScreen 2com.homework.assistant.ui.screens.management.words  WordListScreen 2com.homework.assistant.ui.screens.management.words  fillMaxSize 2com.homework.assistant.ui.screens.management.words  	Alignment ,com.homework.assistant.ui.screens.photocheck  	AppTopBar ,com.homework.assistant.ui.screens.photocheck  Arrangement ,com.homework.assistant.ui.screens.photocheck  Box ,com.homework.assistant.ui.screens.photocheck  Column ,com.homework.assistant.ui.screens.photocheck  
Composable ,com.homework.assistant.ui.screens.photocheck  
MaterialTheme ,com.homework.assistant.ui.screens.photocheck  Modifier ,com.homework.assistant.ui.screens.photocheck  NavigationManager ,com.homework.assistant.ui.screens.photocheck  PhotoCheckCameraScreen ,com.homework.assistant.ui.screens.photocheck  PhotoCheckMainScreen ,com.homework.assistant.ui.screens.photocheck  PhotoCheckResultScreen ,com.homework.assistant.ui.screens.photocheck  Spacer ,com.homework.assistant.ui.screens.photocheck  String ,com.homework.assistant.ui.screens.photocheck  Text ,com.homework.assistant.ui.screens.photocheck  fillMaxSize ,com.homework.assistant.ui.screens.photocheck  height ,com.homework.assistant.ui.screens.photocheck  padding ,com.homework.assistant.ui.screens.photocheck  	Alignment ,com.homework.assistant.ui.screens.recitation  	AppTopBar ,com.homework.assistant.ui.screens.recitation  Arrangement ,com.homework.assistant.ui.screens.recitation  Column ,com.homework.assistant.ui.screens.recitation  
Composable ,com.homework.assistant.ui.screens.recitation  
MaterialTheme ,com.homework.assistant.ui.screens.recitation  Modifier ,com.homework.assistant.ui.screens.recitation  NavigationManager ,com.homework.assistant.ui.screens.recitation  RecitationMainScreen ,com.homework.assistant.ui.screens.recitation  RecitationResultScreen ,com.homework.assistant.ui.screens.recitation  RecitationSessionScreen ,com.homework.assistant.ui.screens.recitation  Spacer ,com.homework.assistant.ui.screens.recitation  String ,com.homework.assistant.ui.screens.recitation  Text ,com.homework.assistant.ui.screens.recitation  fillMaxSize ,com.homework.assistant.ui.screens.recitation  height ,com.homework.assistant.ui.screens.recitation  padding ,com.homework.assistant.ui.screens.recitation  AboutScreen *com.homework.assistant.ui.screens.settings  	Alignment *com.homework.assistant.ui.screens.settings  	AppTopBar *com.homework.assistant.ui.screens.settings  Arrangement *com.homework.assistant.ui.screens.settings  Box *com.homework.assistant.ui.screens.settings  Column *com.homework.assistant.ui.screens.settings  
Composable *com.homework.assistant.ui.screens.settings  
MaterialTheme *com.homework.assistant.ui.screens.settings  Modifier *com.homework.assistant.ui.screens.settings  NavigationManager *com.homework.assistant.ui.screens.settings  PreferencesScreen *com.homework.assistant.ui.screens.settings  SettingsMainScreen *com.homework.assistant.ui.screens.settings  Spacer *com.homework.assistant.ui.screens.settings  StatisticsScreen *com.homework.assistant.ui.screens.settings  Text *com.homework.assistant.ui.screens.settings  fillMaxSize *com.homework.assistant.ui.screens.settings  height *com.homework.assistant.ui.screens.settings  padding *com.homework.assistant.ui.screens.settings  	AppColors com.homework.assistant.ui.theme  	AppShapes com.homework.assistant.ui.theme  
AppTypography com.homework.assistant.ui.theme  Boolean com.homework.assistant.ui.theme  Color com.homework.assistant.ui.theme  
Composable com.homework.assistant.ui.theme  CustomShapes com.homework.assistant.ui.theme  CustomTextStyles com.homework.assistant.ui.theme  DarkColorScheme com.homework.assistant.ui.theme  
FontFamily com.homework.assistant.ui.theme  
FontWeight com.homework.assistant.ui.theme  HomeworkAssistantTheme com.homework.assistant.ui.theme  LightColorScheme com.homework.assistant.ui.theme  
MaterialTheme com.homework.assistant.ui.theme  RoundedCornerShape com.homework.assistant.ui.theme  	TextStyle com.homework.assistant.ui.theme  Unit com.homework.assistant.ui.theme  
Background )com.homework.assistant.ui.theme.AppColors  Color )com.homework.assistant.ui.theme.AppColors  DarkBackground )com.homework.assistant.ui.theme.AppColors  DarkOnBackground )com.homework.assistant.ui.theme.AppColors  
DarkOnSurface )com.homework.assistant.ui.theme.AppColors  DarkSurface )com.homework.assistant.ui.theme.AppColors  Error )com.homework.assistant.ui.theme.AppColors  Gray100 )com.homework.assistant.ui.theme.AppColors  Gray200 )com.homework.assistant.ui.theme.AppColors  Gray300 )com.homework.assistant.ui.theme.AppColors  Gray400 )com.homework.assistant.ui.theme.AppColors  Gray600 )com.homework.assistant.ui.theme.AppColors  Gray700 )com.homework.assistant.ui.theme.AppColors  Gray800 )com.homework.assistant.ui.theme.AppColors  OnBackground )com.homework.assistant.ui.theme.AppColors  	OnPrimary )com.homework.assistant.ui.theme.AppColors  OnSecondary )com.homework.assistant.ui.theme.AppColors  	OnSurface )com.homework.assistant.ui.theme.AppColors  Primary )com.homework.assistant.ui.theme.AppColors  PrimaryVariant )com.homework.assistant.ui.theme.AppColors  	Secondary )com.homework.assistant.ui.theme.AppColors  SecondaryVariant )com.homework.assistant.ui.theme.AppColors  Success )com.homework.assistant.ui.theme.AppColors  Surface )com.homework.assistant.ui.theme.AppColors  RoundedCornerShape ,com.homework.assistant.ui.theme.CustomShapes  dp ,com.homework.assistant.ui.theme.CustomShapes  
FontFamily 0com.homework.assistant.ui.theme.CustomTextStyles  
FontWeight 0com.homework.assistant.ui.theme.CustomTextStyles  
PinyinDisplay 0com.homework.assistant.ui.theme.CustomTextStyles  	TextStyle 0com.homework.assistant.ui.theme.CustomTextStyles  WordDisplay 0com.homework.assistant.ui.theme.CustomTextStyles  sp 0com.homework.assistant.ui.theme.CustomTextStyles  	ByteArray 3homeworkassistantkmp.composeapp.generated.resources  ExperimentalResourceApi 3homeworkassistantkmp.composeapp.generated.resources  OptIn 3homeworkassistantkmp.composeapp.generated.resources  String 3homeworkassistantkmp.composeapp.generated.resources  getResourceUri 3homeworkassistantkmp.composeapp.generated.resources  org 3homeworkassistantkmp.composeapp.generated.resources  readResourceBytes 3homeworkassistantkmp.composeapp.generated.resources  	ByteArray 7homeworkassistantkmp.composeapp.generated.resources.Res  ExperimentalResourceApi 7homeworkassistantkmp.composeapp.generated.resources.Res  String 7homeworkassistantkmp.composeapp.generated.resources.Res  getResourceUri 7homeworkassistantkmp.composeapp.generated.resources.Res  readResourceBytes 7homeworkassistantkmp.composeapp.generated.resources.Res  
BigDecimal 	java.math  
BigInteger 	java.math  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  Result kotlin  
ShortArray kotlin  String kotlin  	Throwable kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  map kotlin  minus kotlin  plus kotlin  not kotlin.Boolean  sp 
kotlin.Double  toInt 
kotlin.Double  div kotlin.Float  rangeTo kotlin.Float  toInt kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  dec 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  div kotlin.Long  rem kotlin.Long  	getOrNull 
kotlin.Result  	isSuccess 
kotlin.Result  	Companion 
kotlin.String  format 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  plus 
kotlin.String  split 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  List kotlin.collections  Map kotlin.collections  Set kotlin.collections  average kotlin.collections  count kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  minus kotlin.collections  plus kotlin.collections  setOf kotlin.collections  sumOf kotlin.collections  	sumOfLong kotlin.collections  average kotlin.collections.List  count kotlin.collections.List  filter kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  sumOf kotlin.collections.List  contains kotlin.collections.Set  
isNotEmpty kotlin.collections.Set  minus kotlin.collections.Set  plus kotlin.collections.Set  size kotlin.collections.Set  CoroutineContext kotlin.coroutines  SuspendFunction0 kotlin.coroutines  SuspendFunction1 kotlin.coroutines  invoke "kotlin.coroutines.SuspendFunction0  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  rangeTo 
kotlin.ranges  KMutableProperty0 kotlin.reflect  Sequence kotlin.sequences  average kotlin.sequences  count kotlin.sequences  filter kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  minus kotlin.sequences  plus kotlin.sequences  sumOf kotlin.sequences  count kotlin.text  filter kotlin.text  format kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  plus kotlin.text  split kotlin.text  sumOf kotlin.text  CoroutineExceptionHandler kotlinx.coroutines  CoroutineScope kotlinx.coroutines  GlobalScope kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  
_isLoading !kotlinx.coroutines.CoroutineScope  
clearError !kotlinx.coroutines.CoroutineScope  
collectLatest !kotlinx.coroutines.CoroutineScope  dictationService !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  updateState !kotlinx.coroutines.CoroutineScope  launch kotlinx.coroutines.GlobalScope  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  
collectLatest kotlinx.coroutines.flow  
collectLatest kotlinx.coroutines.flow.Flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  ExperimentalResourceApi org.jetbrains.compose.resources  InternalResourceApi org.jetbrains.compose.resources  getResourceUri org.jetbrains.compose.resources  readResourceBytes org.jetbrains.compose.resources  Preview (org.jetbrains.compose.ui.tooling.preview                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           