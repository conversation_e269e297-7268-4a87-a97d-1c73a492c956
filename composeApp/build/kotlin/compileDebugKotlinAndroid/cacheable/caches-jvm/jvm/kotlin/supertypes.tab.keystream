,com.homework.assistant.ui.base.BaseViewModel0com.homework.assistant.ui.base.LoadingState.Idle3com.homework.assistant.ui.base.LoadingState.Loading3com.homework.assistant.ui.base.LoadingState.Success1com.homework.assistant.ui.base.LoadingState.Error3com.homework.assistant.ui.base.UiEffect.ShowMessage1com.homework.assistant.ui.base.UiEffect.ShowError4com.homework.assistant.ui.base.UiEffect.NavigateBack0com.homework.assistant.ui.base.UiEffect.Navigate7com.homework.assistant.ui.base.BaseViewModelWithEffects*com.homework.assistant.ui.base.PagingState(com.homework.assistant.ui.base.FormState*com.homework.assistant.ui.base.SearchState>com.homework.assistant.ui.screens.dictation.DictationViewModel<com.homework.assistant.ui.screens.dictation.DictationUiState<com.homework.assistant.ui.screens.dictation.DictationUiEventKcom.homework.assistant.ui.screens.dictation.DictationUiEvent.StartDictationMcom.homework.assistant.ui.screens.dictation.DictationUiEvent.SpeakCurrentWordGcom.homework.assistant.ui.screens.dictation.DictationUiEvent.RepeatWordHcom.homework.assistant.ui.screens.dictation.DictationUiEvent.SpeakPinyinHcom.homework.assistant.ui.screens.dictation.DictationUiEvent.ShowMeaningHcom.homework.assistant.ui.screens.dictation.DictationUiEvent.HideMeaningEcom.homework.assistant.ui.screens.dictation.DictationUiEvent.NextWordIcom.homework.assistant.ui.screens.dictation.DictationUiEvent.PreviousWordLcom.homework.assistant.ui.screens.dictation.DictationUiEvent.UpdateUserInputIcom.homework.assistant.ui.screens.dictation.DictationUiEvent.SubmitAnswerKcom.homework.assistant.ui.screens.dictation.DictationUiEvent.PauseDictationLcom.homework.assistant.ui.screens.dictation.DictationUiEvent.ResumeDictationLcom.homework.assistant.ui.screens.dictation.DictationUiEvent.FinishDictationOcom.homework.assistant.ui.screens.dictation.DictationUiEvent.HandleVoiceCommand#com.homework.assistant.MainActivity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              