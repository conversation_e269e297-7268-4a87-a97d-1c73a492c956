androidx.lifecycle.ViewModel+com.homework.assistant.ui.base.LoadingState'com.homework.assistant.ui.base.UiEffect,com.homework.assistant.ui.base.BaseViewModel&com.homework.assistant.ui.base.UiState&com.homework.assistant.ui.base.UiEvent<com.homework.assistant.ui.screens.dictation.DictationUiEvent>com.homework.assistant.ui.screens.management.ManagementUiEvent>com.homework.assistant.ui.screens.recitation.RecitationUiEvent#androidx.activity.ComponentActivity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     