-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:2:1-43:12
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:2:1-43:12
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:2:1-43:12
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:2:1-43:12
MERGED from [:shared] /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/2481a6ed8bcc819ca9db4052e920b461/transformed/activity-1.9.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/155b9eeded489d827dec38e062689128/transformed/navigation-common-2.7.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/5bc19a0fa7ab0205c2c6637f367a1033/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/95f2d0b463f21a28a826cafa489dc09c/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/1a9b146afe0cb8c8e59669ab167fdd43/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/f0bd2a60d1a4c0d9f2683beea0cdb1f6/transformed/navigation-compose-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [org.jetbrains.compose.components:components-resources-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/47d64a19c4af8fa51ed45cbab1ed90c1/transformed/library-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/d9fe45990b23976bdd658b1f143dfe19/transformed/material3-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/9e9d407faf447fc488e80b42224bb032/transformed/material-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/17b8a1a5bf0ea0c57769ed93a6b58116/transformed/material-icons-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/5a6749a0d34ca7f451d583b77787a3e8/transformed/material-ripple-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/692d8a3950b3c4540ee4eba460b19c99/transformed/animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/b7080a08fe892b5b4cc57d7ae73e3793/transformed/animation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/3eecb841caa21e76893ff30531e170d5/transformed/foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/050ca753048aea9da18b3f78d588083d/transformed/foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/a3f07aebef1520a0f5e469f8c12f9b1f/transformed/ui-tooling-data-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/8095305ba88a6d26a7d411874e7c1d72/transformed/ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/eeffb3b170e8ec0b2a9a48558e0e9c55/transformed/ui-geometry-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/2feec14e06ecd48dbba08c9a866fc2b0/transformed/ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/93850bfe58bd54208948b30c5d01cedd/transformed/ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/87380224a8c1670bd863cec4bcbdd173/transformed/ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/6ae78bd56b74c7f242afae16bb13ff88/transformed/ui-tooling-release/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/a0d289cd62c5d8fe1dc02eba3383022e/transformed/ui-tooling-preview-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/ee47b1cb7f0d7a04d742911802996328/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/a53e3091db5b8f35ceba22abdebe35b9/transformed/core-ktx-1.13.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/5a8234df573eb06c39d09abfe96a27f9/transformed/autofill-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/674f13d696f427c402fc68d4664576b0/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/f5ac14cb634b26ee701b184e199f2eb7/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/81573ca161f83b942be940dd6469f94f/transformed/lifecycle-livedata-core-2.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/00d90eb548ca6ce53b9080cb3051b3de/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/516b076b3fe8b7c991f0cde2c57c7199/transformed/lifecycle-runtime-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/35096b2bb0a24e8e03863ae8afba876b/transformed/lifecycle-process-2.8.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/00a376a96c82fe2a35298dcb9f6f2280/transformed/lifecycle-viewmodel-savedstate-2.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/aabc26d16f7ac372a6dff6a03af8a48b/transformed/lifecycle-viewmodel-ktx-2.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/6184d50c47bf6768bd3542c27371c361/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/27d622fdee3bd74636ba662cf16fa82a/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/02566abb37c0cbe68ae13025eeb68ccd/transformed/lifecycle-viewmodel-compose-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/6a0c0a2e43f6b23e010051408852af3d/transformed/ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/ca912591460d2d60c7efea2ec0791e9b/transformed/activity-ktx-1.9.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/dc9616ae41383a5fcc10742afae6cacd/transformed/activity-compose-1.9.0/AndroidManifest.xml:2:1-7:12
MERGED from [org.jetbrains.compose.components:components-ui-tooling-preview-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/bf1a2b72425444bc8dcad03592257e83/transformed/library-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/2ae498ffe08b5b821f6241b69a7c7963/transformed/runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/062d48df828e2832930b7deba3586607/transformed/runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-4/939086efe7b850ffeb15d85fa649df84/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/59e13a653a5605790c6a6a6808b857a3/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/e88d07a70ba2b7e8d3adfed68f1d8d96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/137c831078714493831bc629885153ed/transformed/tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/7ea8f14f38710541337060aeb753fc8b/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-4/fdd1c2fd02dde4f2da67d2c84c5454ec/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:5:5-67
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:6:5-79
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:6:22-76
uses-permission#android.permission.RECORD_AUDIO
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:9:5-71
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:9:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:10:5-80
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:10:22-77
uses-permission#android.permission.CAMERA
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:13:5-65
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:13:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:16:5-80
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:16:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:17:5-81
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:17:22-78
uses-feature#android.hardware.camera
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:20:5-22:36
	android:required
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:22:9-33
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:21:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:23:5-25:36
	android:required
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:25:9-33
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:24:9-57
application
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:27:5-41:19
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:27:5-41:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/6ae78bd56b74c7f242afae16bb13ff88/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/6ae78bd56b74c7f242afae16bb13ff88/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/35096b2bb0a24e8e03863ae8afba876b/transformed/lifecycle-process-2.8.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/35096b2bb0a24e8e03863ae8afba876b/transformed/lifecycle-process-2.8.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/59e13a653a5605790c6a6a6808b857a3/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/59e13a653a5605790c6a6a6808b857a3/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/e88d07a70ba2b7e8d3adfed68f1d8d96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/e88d07a70ba2b7e8d3adfed68f1d8d96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:30:9-35
	android:label
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:29:9-41
	android:allowBackup
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:28:9-35
activity#com.homework.assistant.MainActivity
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:32:9-40:20
	android:launchMode
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:35:13-43
	android:exported
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:34:13-36
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:33:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:36:13-39:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:37:17-69
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:37:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:38:17-77
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:38:27-74
uses-sdk
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
MERGED from [:shared] /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared] /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/2481a6ed8bcc819ca9db4052e920b461/transformed/activity-1.9.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/2481a6ed8bcc819ca9db4052e920b461/transformed/activity-1.9.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/155b9eeded489d827dec38e062689128/transformed/navigation-common-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/155b9eeded489d827dec38e062689128/transformed/navigation-common-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/5bc19a0fa7ab0205c2c6637f367a1033/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/5bc19a0fa7ab0205c2c6637f367a1033/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/95f2d0b463f21a28a826cafa489dc09c/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/95f2d0b463f21a28a826cafa489dc09c/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/1a9b146afe0cb8c8e59669ab167fdd43/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/1a9b146afe0cb8c8e59669ab167fdd43/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/f0bd2a60d1a4c0d9f2683beea0cdb1f6/transformed/navigation-compose-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/transforms-4/f0bd2a60d1a4c0d9f2683beea0cdb1f6/transformed/navigation-compose-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.components:components-resources-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/47d64a19c4af8fa51ed45cbab1ed90c1/transformed/library-release/AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.components:components-resources-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/47d64a19c4af8fa51ed45cbab1ed90c1/transformed/library-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/d9fe45990b23976bdd658b1f143dfe19/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/d9fe45990b23976bdd658b1f143dfe19/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/9e9d407faf447fc488e80b42224bb032/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/9e9d407faf447fc488e80b42224bb032/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/17b8a1a5bf0ea0c57769ed93a6b58116/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/17b8a1a5bf0ea0c57769ed93a6b58116/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/5a6749a0d34ca7f451d583b77787a3e8/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/5a6749a0d34ca7f451d583b77787a3e8/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/692d8a3950b3c4540ee4eba460b19c99/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/692d8a3950b3c4540ee4eba460b19c99/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/b7080a08fe892b5b4cc57d7ae73e3793/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/b7080a08fe892b5b4cc57d7ae73e3793/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/3eecb841caa21e76893ff30531e170d5/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/3eecb841caa21e76893ff30531e170d5/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/050ca753048aea9da18b3f78d588083d/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/050ca753048aea9da18b3f78d588083d/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/a3f07aebef1520a0f5e469f8c12f9b1f/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/a3f07aebef1520a0f5e469f8c12f9b1f/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/8095305ba88a6d26a7d411874e7c1d72/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/8095305ba88a6d26a7d411874e7c1d72/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/eeffb3b170e8ec0b2a9a48558e0e9c55/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/eeffb3b170e8ec0b2a9a48558e0e9c55/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/2feec14e06ecd48dbba08c9a866fc2b0/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/2feec14e06ecd48dbba08c9a866fc2b0/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/93850bfe58bd54208948b30c5d01cedd/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/93850bfe58bd54208948b30c5d01cedd/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/87380224a8c1670bd863cec4bcbdd173/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/87380224a8c1670bd863cec4bcbdd173/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/6ae78bd56b74c7f242afae16bb13ff88/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/6ae78bd56b74c7f242afae16bb13ff88/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/a0d289cd62c5d8fe1dc02eba3383022e/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/a0d289cd62c5d8fe1dc02eba3383022e/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/ee47b1cb7f0d7a04d742911802996328/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/ee47b1cb7f0d7a04d742911802996328/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/a53e3091db5b8f35ceba22abdebe35b9/transformed/core-ktx-1.13.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/a53e3091db5b8f35ceba22abdebe35b9/transformed/core-ktx-1.13.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/5a8234df573eb06c39d09abfe96a27f9/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/5a8234df573eb06c39d09abfe96a27f9/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/674f13d696f427c402fc68d4664576b0/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/674f13d696f427c402fc68d4664576b0/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/f5ac14cb634b26ee701b184e199f2eb7/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/f5ac14cb634b26ee701b184e199f2eb7/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/81573ca161f83b942be940dd6469f94f/transformed/lifecycle-livedata-core-2.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/81573ca161f83b942be940dd6469f94f/transformed/lifecycle-livedata-core-2.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/00d90eb548ca6ce53b9080cb3051b3de/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/00d90eb548ca6ce53b9080cb3051b3de/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/516b076b3fe8b7c991f0cde2c57c7199/transformed/lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/516b076b3fe8b7c991f0cde2c57c7199/transformed/lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/35096b2bb0a24e8e03863ae8afba876b/transformed/lifecycle-process-2.8.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/35096b2bb0a24e8e03863ae8afba876b/transformed/lifecycle-process-2.8.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/00a376a96c82fe2a35298dcb9f6f2280/transformed/lifecycle-viewmodel-savedstate-2.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/00a376a96c82fe2a35298dcb9f6f2280/transformed/lifecycle-viewmodel-savedstate-2.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/aabc26d16f7ac372a6dff6a03af8a48b/transformed/lifecycle-viewmodel-ktx-2.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/aabc26d16f7ac372a6dff6a03af8a48b/transformed/lifecycle-viewmodel-ktx-2.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/6184d50c47bf6768bd3542c27371c361/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/6184d50c47bf6768bd3542c27371c361/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/27d622fdee3bd74636ba662cf16fa82a/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/27d622fdee3bd74636ba662cf16fa82a/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/02566abb37c0cbe68ae13025eeb68ccd/transformed/lifecycle-viewmodel-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/02566abb37c0cbe68ae13025eeb68ccd/transformed/lifecycle-viewmodel-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/6a0c0a2e43f6b23e010051408852af3d/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/6a0c0a2e43f6b23e010051408852af3d/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/ca912591460d2d60c7efea2ec0791e9b/transformed/activity-ktx-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/ca912591460d2d60c7efea2ec0791e9b/transformed/activity-ktx-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/dc9616ae41383a5fcc10742afae6cacd/transformed/activity-compose-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/dc9616ae41383a5fcc10742afae6cacd/transformed/activity-compose-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.components:components-ui-tooling-preview-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/bf1a2b72425444bc8dcad03592257e83/transformed/library-release/AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.components:components-ui-tooling-preview-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/bf1a2b72425444bc8dcad03592257e83/transformed/library-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/2ae498ffe08b5b821f6241b69a7c7963/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/2ae498ffe08b5b821f6241b69a7c7963/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/062d48df828e2832930b7deba3586607/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/062d48df828e2832930b7deba3586607/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-4/939086efe7b850ffeb15d85fa649df84/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-4/939086efe7b850ffeb15d85fa649df84/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/59e13a653a5605790c6a6a6808b857a3/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/59e13a653a5605790c6a6a6808b857a3/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/e88d07a70ba2b7e8d3adfed68f1d8d96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/e88d07a70ba2b7e8d3adfed68f1d8d96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/137c831078714493831bc629885153ed/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/137c831078714493831bc629885153ed/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/7ea8f14f38710541337060aeb753fc8b/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/7ea8f14f38710541337060aeb753fc8b/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-4/fdd1c2fd02dde4f2da67d2c84c5454ec/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-4/fdd1c2fd02dde4f2da67d2c84c5454ec/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/6ae78bd56b74c7f242afae16bb13ff88/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/6ae78bd56b74c7f242afae16bb13ff88/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/6ae78bd56b74c7f242afae16bb13ff88/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/35096b2bb0a24e8e03863ae8afba876b/transformed/lifecycle-process-2.8.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/35096b2bb0a24e8e03863ae8afba876b/transformed/lifecycle-process-2.8.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/e88d07a70ba2b7e8d3adfed68f1d8d96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/e88d07a70ba2b7e8d3adfed68f1d8d96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/3d15d406c514d885850b9f9938cd1687/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:23:9-81
permission#com.homework.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:26:22-94
uses-permission#com.homework.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/35096b2bb0a24e8e03863ae8afba876b/transformed/lifecycle-process-2.8.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/35096b2bb0a24e8e03863ae8afba876b/transformed/lifecycle-process-2.8.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.0] /Users/<USER>/.gradle/caches/transforms-4/35096b2bb0a24e8e03863ae8afba876b/transformed/lifecycle-process-2.8.0/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/877db228a0fc180d202382197fc04bb0/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
