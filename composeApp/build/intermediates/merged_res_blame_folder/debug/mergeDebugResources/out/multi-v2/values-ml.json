{"logs": [{"outputFile": "com.homework.assistant.composeApp-mergeDebugResources-48:/values-ml/values-ml.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-4/d9fe45990b23976bdd658b1f143dfe19/transformed/material3-release/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,301,427,550,650,744,855,1007,1125,1282,1367,1472,1572,1674,1797,1930,2040,2176,2318,2449,2653,2787,2911,3041,3175,3276,3374,3492,3623,3722,3824,3937,4075,4221,4335,4444,4520,4618,4718,4805,4902,5010,5090,5178,5276,5389,5484,5595,5685,5800,5902,6015,6147,6227,6334", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "170,296,422,545,645,739,850,1002,1120,1277,1362,1467,1567,1669,1792,1925,2035,2171,2313,2444,2648,2782,2906,3036,3170,3271,3369,3487,3618,3717,3819,3932,4070,4216,4330,4439,4515,4613,4713,4800,4897,5005,5085,5173,5271,5384,5479,5590,5680,5795,5897,6010,6142,6222,6329,6426"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1504,1624,1750,1876,1999,2099,2193,2304,2456,2574,2731,2816,2921,3021,3123,3246,3379,3489,3625,3767,3898,4102,4236,4360,4490,4624,4725,4823,4941,5072,5171,5273,5386,5524,5670,5784,5893,5969,6067,6167,6254,6351,6459,6539,6627,6725,6838,6933,7044,7134,7249,7351,7464,7596,7676,7783", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "1619,1745,1871,1994,2094,2188,2299,2451,2569,2726,2811,2916,3016,3118,3241,3374,3484,3620,3762,3893,4097,4231,4355,4485,4619,4720,4818,4936,5067,5166,5268,5381,5519,5665,5779,5888,5964,6062,6162,6249,6346,6454,6534,6622,6720,6833,6928,7039,7129,7244,7346,7459,7591,7671,7778,7875"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,310,412,516,619,720,8440", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "202,305,407,511,614,715,837,8536"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/6a0c0a2e43f6b23e010051408852af3d/transformed/ui-release/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,287,386,490,580,666,767,854,942,1009,1076,1162,1249,1327,1403,1470", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "195,282,381,485,575,661,762,849,937,1004,1071,1157,1244,1322,1398,1465,1584"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,937,1024,1123,1227,1317,1403,7880,7967,8055,8122,8189,8275,8362,8541,8617,8684", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "932,1019,1118,1222,1312,1398,1499,7962,8050,8117,8184,8270,8357,8435,8612,8679,8798"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/050ca753048aea9da18b3f78d588083d/transformed/foundation-release/res/values-ml/values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,91", "endOffsets": "138,230"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8803,8891", "endColumns": "87,91", "endOffsets": "8886,8978"}}]}]}