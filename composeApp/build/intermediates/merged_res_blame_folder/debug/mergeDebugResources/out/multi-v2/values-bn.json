{"logs": [{"outputFile": "com.homework.assistant.composeApp-mergeDebugResources-48:/values-bn/values-bn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-4/6a0c0a2e43f6b23e010051408852af3d/transformed/ui-release/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,283,373,471,557,636,742,829,918,988,1058,1136,1217,1300,1375,1443", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "194,278,368,466,552,631,737,824,913,983,1053,1131,1212,1295,1370,1438,1556"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "834,928,1012,1102,1200,1286,1365,7725,7812,7901,7971,8041,8119,8200,8384,8459,8527", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "923,1007,1097,1195,1281,1360,1466,7807,7896,7966,8036,8114,8195,8278,8454,8522,8640"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/d9fe45990b23976bdd658b1f143dfe19/transformed/material3-release/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,193,330,449,583,700,799,915,1057,1178,1320,1405,1511,1605,1706,1835,1964,2075,2204,2331,2461,2641,2763,2883,3005,3136,3231,3326,3459,3606,3703,3808,3918,4045,4177,4284,4385,4462,4565,4665,4756,4846,4949,5029,5114,5215,5319,5412,5517,5604,5710,5809,5917,6035,6115,6215", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "188,325,444,578,695,794,910,1052,1173,1315,1400,1506,1600,1701,1830,1959,2070,2199,2326,2456,2636,2758,2878,3000,3131,3226,3321,3454,3601,3698,3803,3913,4040,4172,4279,4380,4457,4560,4660,4751,4841,4944,5024,5109,5210,5314,5407,5512,5599,5705,5804,5912,6030,6110,6210,6304"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1471,1609,1746,1865,1999,2116,2215,2331,2473,2594,2736,2821,2927,3021,3122,3251,3380,3491,3620,3747,3877,4057,4179,4299,4421,4552,4647,4742,4875,5022,5119,5224,5334,5461,5593,5700,5801,5878,5981,6081,6172,6262,6365,6445,6530,6631,6735,6828,6933,7020,7126,7225,7333,7451,7531,7631", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "1604,1741,1860,1994,2111,2210,2326,2468,2589,2731,2816,2922,3016,3117,3246,3375,3486,3615,3742,3872,4052,4174,4294,4416,4547,4642,4737,4870,5017,5114,5219,5329,5456,5588,5695,5796,5873,5976,6076,6167,6257,6360,6440,6525,6626,6730,6823,6928,7015,7121,7220,7328,7446,7526,7626,7720"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,408,511,612,714,8283", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "199,301,403,506,607,709,829,8379"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/050ca753048aea9da18b3f78d588083d/transformed/foundation-release/res/values-bn/values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8645,8730", "endColumns": "84,84", "endOffsets": "8725,8810"}}]}]}