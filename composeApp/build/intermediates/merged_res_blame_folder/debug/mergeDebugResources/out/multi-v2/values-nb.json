{"logs": [{"outputFile": "com.homework.assistant.composeApp-mergeDebugResources-48:/values-nb/values-nb.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-4/6a0c0a2e43f6b23e010051408852af3d/transformed/ui-release/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,924,1005,1102,1202,1290,1366,7485,7574,7656,7720,7784,7864,7946,8117,8194,8261", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "919,1000,1097,1197,1285,1361,1449,7569,7651,7715,7779,7859,7941,8011,8189,8256,8376"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/d9fe45990b23976bdd658b1f143dfe19/transformed/material3-release/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,394,507,606,700,811,955,1077,1227,1311,1411,1500,1594,1701,1819,1924,2051,2173,2306,2473,2600,2716,2837,2958,3048,3146,3265,3396,3497,3607,3710,3844,3985,4090,4188,4268,4362,4453,4537,4621,4732,4812,4896,4997,5096,5187,5287,5375,5480,5582,5687,5804,5884,5987", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "167,282,389,502,601,695,806,950,1072,1222,1306,1406,1495,1589,1696,1814,1919,2046,2168,2301,2468,2595,2711,2832,2953,3043,3141,3260,3391,3492,3602,3705,3839,3980,4085,4183,4263,4357,4448,4532,4616,4727,4807,4891,4992,5091,5182,5282,5370,5475,5577,5682,5799,5879,5982,6081"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1454,1571,1686,1793,1906,2005,2099,2210,2354,2476,2626,2710,2810,2899,2993,3100,3218,3323,3450,3572,3705,3872,3999,4115,4236,4357,4447,4545,4664,4795,4896,5006,5109,5243,5384,5489,5587,5667,5761,5852,5936,6020,6131,6211,6295,6396,6495,6586,6686,6774,6879,6981,7086,7203,7283,7386", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "1566,1681,1788,1901,2000,2094,2205,2349,2471,2621,2705,2805,2894,2988,3095,3213,3318,3445,3567,3700,3867,3994,4110,4231,4352,4442,4540,4659,4790,4891,5001,5104,5238,5379,5484,5582,5662,5756,5847,5931,6015,6126,6206,6290,6391,6490,6581,6681,6769,6874,6976,7081,7198,7278,7381,7480"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,497,605,711,8016", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "194,296,393,492,600,706,826,8112"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/050ca753048aea9da18b3f78d588083d/transformed/foundation-release/res/values-nb/values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8381,8471", "endColumns": "89,87", "endOffsets": "8466,8554"}}]}]}