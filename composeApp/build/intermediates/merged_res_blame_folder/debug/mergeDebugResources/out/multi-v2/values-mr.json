{"logs": [{"outputFile": "com.homework.assistant.composeApp-mergeDebugResources-48:/values-mr/values-mr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,309,410,513,615,720,8157", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "200,304,405,508,610,715,832,8253"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/6a0c0a2e43f6b23e010051408852af3d/transformed/ui-release/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,281,378,476,563,649,734,823,906,975,1045,1125,1210,1281,1357,1423", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "194,276,373,471,558,644,729,818,901,970,1040,1120,1205,1276,1352,1418,1536"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,931,1013,1110,1208,1295,1381,7610,7699,7782,7851,7921,8001,8086,8258,8334,8400", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "926,1008,1105,1203,1290,1376,1461,7694,7777,7846,7916,7996,8081,8152,8329,8395,8513"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/050ca753048aea9da18b3f78d588083d/transformed/foundation-release/res/values-mr/values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8518,8603", "endColumns": "84,84", "endOffsets": "8598,8683"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/d9fe45990b23976bdd658b1f143dfe19/transformed/material3-release/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,303,414,539,642,742,857,993,1116,1262,1347,1453,1544,1642,1756,1886,1997,2132,2266,2394,2572,2697,2813,2932,3057,3149,3244,3364,3493,3593,3696,3805,3942,4084,4199,4297,4373,4476,4580,4665,4755,4855,4935,5018,5117,5216,5313,5412,5499,5603,5703,5807,5925,6005,6105", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "175,298,409,534,637,737,852,988,1111,1257,1342,1448,1539,1637,1751,1881,1992,2127,2261,2389,2567,2692,2808,2927,3052,3144,3239,3359,3488,3588,3691,3800,3937,4079,4194,4292,4368,4471,4575,4660,4750,4850,4930,5013,5112,5211,5308,5407,5494,5598,5698,5802,5920,6000,6100,6194"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1466,1591,1714,1825,1950,2053,2153,2268,2404,2527,2673,2758,2864,2955,3053,3167,3297,3408,3543,3677,3805,3983,4108,4224,4343,4468,4560,4655,4775,4904,5004,5107,5216,5353,5495,5610,5708,5784,5887,5991,6076,6166,6266,6346,6429,6528,6627,6724,6823,6910,7014,7114,7218,7336,7416,7516", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "1586,1709,1820,1945,2048,2148,2263,2399,2522,2668,2753,2859,2950,3048,3162,3292,3403,3538,3672,3800,3978,4103,4219,4338,4463,4555,4650,4770,4899,4999,5102,5211,5348,5490,5605,5703,5779,5882,5986,6071,6161,6261,6341,6424,6523,6622,6719,6818,6905,7009,7109,7213,7331,7411,7511,7605"}}]}]}