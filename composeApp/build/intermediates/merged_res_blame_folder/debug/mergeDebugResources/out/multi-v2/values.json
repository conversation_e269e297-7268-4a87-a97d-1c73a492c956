{"logs": [{"outputFile": "com.homework.assistant.composeApp-mergeDebugResources-48:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-4/c3938884f9b8d21b9bac608a4c18eb9a/transformed/core-1.13.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,72,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,98,101,102,103,104,105,106,107,182,198,199,203,204,208,209,210,218,224,234,267,288,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,370,435,501,570,633,703,771,843,913,974,1048,1121,1182,1243,1305,1369,1431,1492,1560,1660,1720,1786,1859,1928,1985,2037,2099,2171,2247,2312,2371,2430,2490,2550,2610,2670,2730,2790,2850,2910,2970,3030,3089,3149,3209,3269,3329,3389,3449,3509,3569,3629,3689,3748,3808,3868,3927,3986,4045,4104,4163,4569,4604,4801,4856,4919,4974,5032,5090,5151,5214,5271,5322,5372,5433,5490,5556,5590,5625,5920,6115,6182,6254,6323,6392,6466,6538,11785,12558,12675,12876,12986,13187,13316,13388,13759,13962,14263,15994,16675,17357", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,72,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,98,101,102,103,104,105,106,107,182,198,202,203,207,208,209,210,223,233,266,287,320,326", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,430,496,565,628,698,766,838,908,969,1043,1116,1177,1238,1300,1364,1426,1487,1555,1655,1715,1781,1854,1923,1980,2032,2094,2166,2242,2307,2366,2425,2485,2545,2605,2665,2725,2785,2845,2905,2965,3025,3084,3144,3204,3264,3324,3384,3444,3504,3564,3624,3684,3743,3803,3863,3922,3981,4040,4099,4158,4217,4599,4634,4851,4914,4969,5027,5085,5146,5209,5266,5317,5367,5428,5485,5551,5585,5620,5655,5985,6177,6249,6318,6387,6461,6533,6621,11851,12670,12871,12981,13182,13311,13383,13450,13957,14258,15989,16670,17352,17519"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/e88d07a70ba2b7e8d3adfed68f1d8d96/transformed/startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "99", "startColumns": "4", "startOffsets": "5990", "endColumns": "82", "endOffsets": "6068"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/5bc19a0fa7ab0205c2c6637f367a1033/transformed/navigation-runtime-2.7.7/res/values/values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "74,211,355,358", "startColumns": "4,4,4,4", "startOffsets": "4639,13455,18787,18902", "endLines": "74,217,357,360", "endColumns": "52,24,24,24", "endOffsets": "4687,13754,18897,19012"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/6a0c0a2e43f6b23e010051408852af3d/transformed/ui-release/res/values/values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "66,67,68,69,70,97,108,109,110,111,112,113,114,175,176,177,178,179,180,181,183,184,185,188,191,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4222,4296,4354,4409,4460,5867,6626,6691,6745,6811,6912,6970,7022,11449,11511,11565,11601,11635,11685,11739,11856,11903,11939,12140,12252,12363", "endLines": "66,67,68,69,70,97,108,109,110,111,112,113,114,175,176,177,178,179,180,181,183,184,185,190,193,197", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "4291,4349,4404,4455,4510,5915,6686,6740,6806,6907,6965,7017,7077,11506,11560,11596,11630,11680,11734,11780,11898,11934,12024,12247,12358,12553"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/d9fe45990b23976bdd658b1f143dfe19/transformed/material3-release/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123,124,127,128,129,130,131,132,133,134,135,136,137,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7082,7170,7256,7337,7421,7490,7555,7638,7744,7830,7950,8004,8073,8134,8203,8292,8387,8461,8558,8651,8749,8898,8989,9077,9173,9271,9335,9403,9490,9584,9651,9723,9795,9896,10005,10081,10150,10198,10264,10328,10385,10442,10514,10564,10618,10689,10760,10830,10899,10957,11033,11104,11178,11264,11314,11384", "endLines": "115,116,117,118,119,120,121,122,123,126,127,128,129,130,131,132,133,134,135,136,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "7165,7251,7332,7416,7485,7550,7633,7739,7825,7945,7999,8068,8129,8198,8287,8382,8456,8553,8646,8744,8893,8984,9072,9168,9266,9330,9398,9485,9579,9646,9718,9790,9891,10000,10076,10145,10193,10259,10323,10380,10437,10509,10559,10613,10684,10755,10825,10894,10952,11028,11099,11173,11259,11309,11379,11444"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/155b9eeded489d827dec38e062689128/transformed/navigation-common-2.7.7/res/values/values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "327,340,346,352,361", "startColumns": "4,4,4,4,4", "startOffsets": "17524,18163,18407,18654,19017", "endLines": "339,345,351,354,365", "endColumns": "24,24,24,24,24", "endOffsets": "18158,18402,18649,18782,19194"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/ee47b1cb7f0d7a04d742911802996328/transformed/customview-poolingcontainer-1.0.0/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "71,75", "startColumns": "4,4", "startOffsets": "4515,4692", "endColumns": "53,66", "endOffsets": "4564,4754"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/516b076b3fe8b7c991f0cde2c57c7199/transformed/lifecycle-runtime-release/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "93", "startColumns": "4", "startOffsets": "5660", "endColumns": "42", "endOffsets": "5698"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/f5ac14cb634b26ee701b184e199f2eb7/transformed/savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "95", "startColumns": "4", "startOffsets": "5763", "endColumns": "53", "endOffsets": "5812"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/050ca753048aea9da18b3f78d588083d/transformed/foundation-release/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "186,187", "startColumns": "4,4", "startOffsets": "12029,12085", "endColumns": "55,54", "endOffsets": "12080,12135"}}, {"source": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/res/values/strings.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "41", "endOffsets": "92"}, "to": {"startLines": "100", "startColumns": "4", "startOffsets": "6073", "endColumns": "41", "endOffsets": "6110"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/2481a6ed8bcc819ca9db4052e920b461/transformed/activity-1.9.0/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "76,94", "startColumns": "4,4", "startOffsets": "4759,5703", "endColumns": "41,59", "endOffsets": "4796,5758"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/27d622fdee3bd74636ba662cf16fa82a/transformed/lifecycle-viewmodel-release/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "5817", "endColumns": "49", "endOffsets": "5862"}}]}]}